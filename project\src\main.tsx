import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import UnifiedPOSSystem from './UnifiedPOSSystem';
import IndustryStandardPOSSystem from './IndustryStandardPOSSystem';
import './index.css';
import './styles/industry-standard-pos.css';

// Check if we should load the industry-standard POS system
const useIndustryStandard = window.location.pathname.includes('industry-standard') ||
                           window.location.search.includes('industry=true') ||
                           localStorage.getItem('useIndustryStandardPOS') === 'true';

// Log which system is being loaded
console.log(`🚀 Loading ${useIndustryStandard ? 'Industry Standard' : 'Legacy'} POS System`);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {useIndustryStandard ? (
      <IndustryStandardPOSSystem
        initialTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
        companyBranding={{
          companyName: 'RestroFlow',
          primaryColor: '#3B82F6',
          secondaryColor: '#10B981'
        }}
      />
    ) : (
      <UnifiedPOSSystem />
    )}
  </StrictMode>
);
