import React, { useState } from 'react';
import { 
  ArrowRight, 
  Star, 
  Users, 
  Clock, 
  MapPin, 
  Truck, 
  Calendar, 
  Building,
  Wine,
  Coffee,
  Music,
  Zap,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { BusinessType, INDUSTRY_THEMES, IndustryThemeProvider } from '../contexts/IndustryThemeContext';
import BusinessTypeSelector from './BusinessTypeSelector';
import IndustryFeatureManager from './IndustryFeatureManager';

// Mock business types for demo
const DEMO_BUSINESS_TYPES: BusinessType[] = [
  {
    id: '1',
    name: 'Fine Dining Restaurant',
    code: 'FINE_DINING',
    description: 'Upscale restaurants with table service, wine pairings, and multi-course meals',
    icon: '🍽️',
    colorScheme: 'burgundy',
    defaultTheme: INDUSTRY_THEMES.FINE_DINING,
    featureSet: {
      wineManagement: true,
      courseTiming: true,
      guestProfiles: true,
      sommelierNotes: true,
      tableSideService: true
    },
    workflowConfig: {
      serviceType: 'table',
      orderFlow: 'multi_course',
      paymentTiming: 'end_of_meal',
      reservationRequired: true
    }
  },
  {
    id: '2',
    name: 'Quick Service Restaurant',
    code: 'QUICK_SERVICE',
    description: 'Fast food restaurants with counter service and quick turnover',
    icon: '⚡',
    colorScheme: 'orange',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE,
    featureSet: {
      orderQueue: true,
      kitchenDisplay: true,
      mobileOrdering: true,
      loyaltyProgram: true,
      driveThrough: true
    },
    workflowConfig: {
      serviceType: 'counter',
      orderFlow: 'single_step',
      paymentTiming: 'immediate',
      reservationRequired: false
    }
  }
];

interface IndustryShowcaseProps {
  onBusinessTypeSelect?: (businessType: BusinessType) => void;
}

const IndustryShowcase: React.FC<IndustryShowcaseProps> = ({
  onBusinessTypeSelect
}) => {
  const [selectedBusinessType, setSelectedBusinessType] = useState<BusinessType | null>(null);
  const [currentView, setCurrentView] = useState<'selector' | 'features' | 'preview'>('selector');
  const [isPlaying, setIsPlaying] = useState(false);

  const handleBusinessTypeSelect = (businessType: BusinessType) => {
    setSelectedBusinessType(businessType);
    setCurrentView('features');
    onBusinessTypeSelect?.(businessType);
  };

  const handleFeatureToggle = (featureId: string, enabled: boolean) => {
    if (selectedBusinessType) {
      setSelectedBusinessType({
        ...selectedBusinessType,
        featureSet: {
          ...selectedBusinessType.featureSet,
          [featureId]: enabled
        }
      });
    }
  };

  const handleConfigurationChange = (featureId: string, config: Record<string, any>) => {
    console.log('Configuration changed for', featureId, config);
  };

  const resetDemo = () => {
    setSelectedBusinessType(null);
    setCurrentView('selector');
    setIsPlaying(false);
  };

  const startDemo = () => {
    setIsPlaying(true);
    // Auto-progress through demo steps
    setTimeout(() => {
      if (!selectedBusinessType) {
        handleBusinessTypeSelect(DEMO_BUSINESS_TYPES[0]);
      }
    }, 1000);
  };

  const renderHeader = () => (
    <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-8 rounded-2xl shadow-xl mb-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold mb-2">Industry-Specific POS Showcase</h1>
          <p className="text-blue-100 text-lg">
            Experience how RESTROFLOW adapts to different restaurant and hospitality business types
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={startDemo}
            disabled={isPlaying}
            className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 disabled:opacity-50"
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            <span>{isPlaying ? 'Demo Running' : 'Start Demo'}</span>
          </button>
          <button
            onClick={resetDemo}
            className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2"
          >
            <RotateCcw className="w-5 h-5" />
            <span>Reset</span>
          </button>
        </div>
      </div>
      
      {selectedBusinessType && (
        <div className="mt-6 p-4 bg-white/10 rounded-xl backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{selectedBusinessType.icon}</div>
            <div>
              <h3 className="text-xl font-bold">{selectedBusinessType.name}</h3>
              <p className="text-blue-100">{selectedBusinessType.description}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderProgressSteps = () => (
    <div className="flex items-center justify-center space-x-4 mb-8">
      {[
        { id: 'selector', label: 'Select Business Type', icon: Users },
        { id: 'features', label: 'Configure Features', icon: Star },
        { id: 'preview', label: 'Preview Interface', icon: MapPin }
      ].map((step, index) => {
        const Icon = step.icon;
        const isActive = currentView === step.id;
        const isCompleted = 
          (step.id === 'selector' && selectedBusinessType) ||
          (step.id === 'features' && currentView === 'preview');
        
        return (
          <React.Fragment key={step.id}>
            <div
              className={`flex items-center space-x-3 px-4 py-2 rounded-xl transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : isCompleted
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-600'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="font-medium">{step.label}</span>
            </div>
            {index < 2 && (
              <ArrowRight className={`w-5 h-5 ${isCompleted ? 'text-green-600' : 'text-gray-400'}`} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );

  const renderBusinessTypeComparison = () => (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 mb-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Industry Comparison</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            name: 'Fine Dining',
            icon: Wine,
            color: 'red',
            features: ['Wine Management', 'Course Timing', 'Guest Profiles'],
            workflow: 'Multi-course service'
          },
          {
            name: 'Quick Service',
            icon: Zap,
            color: 'orange',
            features: ['Order Queue', 'Kitchen Display', 'Mobile Orders'],
            workflow: 'Fast counter service'
          },
          {
            name: 'Cafe',
            icon: Coffee,
            color: 'amber',
            features: ['Beverage Custom', 'Pre-ordering', 'Subscriptions'],
            workflow: 'Beverage-focused'
          },
          {
            name: 'Bar',
            icon: Music,
            color: 'blue',
            features: ['Alcohol Tracking', 'Age Verification', 'Tab Management'],
            workflow: 'Tab-based service'
          }
        ].map((industry) => {
          const Icon = industry.icon;
          return (
            <div
              key={industry.name}
              className={`p-6 rounded-xl border-2 transition-all duration-200 hover:shadow-lg ${
                industry.color === 'red' ? 'border-red-200 bg-red-50 hover:border-red-300' :
                industry.color === 'orange' ? 'border-orange-200 bg-orange-50 hover:border-orange-300' :
                industry.color === 'amber' ? 'border-amber-200 bg-amber-50 hover:border-amber-300' :
                'border-blue-200 bg-blue-50 hover:border-blue-300'
              }`}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className={`p-2 rounded-lg ${
                  industry.color === 'red' ? 'bg-red-600 text-white' :
                  industry.color === 'orange' ? 'bg-orange-600 text-white' :
                  industry.color === 'amber' ? 'bg-amber-600 text-white' :
                  'bg-blue-600 text-white'
                }`}>
                  <Icon className="w-5 h-5" />
                </div>
                <h3 className="font-bold text-gray-900">{industry.name}</h3>
              </div>
              
              <div className="space-y-2 mb-4">
                {industry.features.map((feature) => (
                  <div key={feature} className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className={`w-2 h-2 rounded-full ${
                      industry.color === 'red' ? 'bg-red-400' :
                      industry.color === 'orange' ? 'bg-orange-400' :
                      industry.color === 'amber' ? 'bg-amber-400' :
                      'bg-blue-400'
                    }`}></div>
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
              
              <div className={`text-sm font-medium ${
                industry.color === 'red' ? 'text-red-700' :
                industry.color === 'orange' ? 'text-orange-700' :
                industry.color === 'amber' ? 'text-amber-700' :
                'text-blue-700'
              }`}>
                {industry.workflow}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case 'selector':
        return (
          <div>
            {renderBusinessTypeComparison()}
            <BusinessTypeSelector
              onSelect={handleBusinessTypeSelect}
              selectedType={selectedBusinessType}
              showDetails={true}
            />
          </div>
        );
      
      case 'features':
        return selectedBusinessType ? (
          <div>
            <IndustryFeatureManager
              businessType={selectedBusinessType}
              onFeatureToggle={handleFeatureToggle}
              onConfigurationChange={handleConfigurationChange}
            />
            <div className="mt-8 text-center">
              <button
                onClick={() => setCurrentView('preview')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                Preview Interface
              </button>
            </div>
          </div>
        ) : null;
      
      case 'preview':
        return (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Interface Preview</h2>
            <div className="bg-gray-100 rounded-xl p-8 text-center">
              <div className="text-6xl mb-4">{selectedBusinessType?.icon}</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                {selectedBusinessType?.name} Interface
              </h3>
              <p className="text-gray-600 mb-6">
                Customized POS interface with industry-specific features and workflows
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-gray-900 mb-2">Theme Applied</h4>
                  <p className="text-sm text-gray-600">
                    {selectedBusinessType?.colorScheme} color scheme
                  </p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-gray-900 mb-2">Features Enabled</h4>
                  <p className="text-sm text-gray-600">
                    {Object.values(selectedBusinessType?.featureSet || {}).filter(Boolean).length} active features
                  </p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-gray-900 mb-2">Workflow Type</h4>
                  <p className="text-sm text-gray-600">
                    {selectedBusinessType?.workflowConfig.serviceType} service
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <IndustryThemeProvider initialBusinessType={selectedBusinessType}>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 p-8">
        <div className="max-w-7xl mx-auto">
          {renderHeader()}
          {renderProgressSteps()}
          {renderCurrentView()}
        </div>
      </div>
    </IndustryThemeProvider>
  );
};

export default IndustryShowcase;
