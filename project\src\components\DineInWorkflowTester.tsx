import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  Play, 
  RefreshCw,
  Users,
  MapPin,
  Shield,
  CreditCard
} from 'lucide-react';

interface TestScenario {
  id: string;
  name: string;
  description: string;
  steps: string[];
  status: 'pending' | 'running' | 'passed' | 'failed';
  error?: string;
  duration?: number;
}

const DineInWorkflowTester: React.FC = () => {
  const { state, apiCall } = useEnhancedAppContext();
  const [scenarios, setScenarios] = useState<TestScenario[]>([
    {
      id: 'basic-workflow',
      name: 'Basic Dine-In Workflow',
      description: 'Test complete dine-in workflow from table selection to order completion',
      steps: [
        'Employee login verification',
        'Fetch available tables',
        'Select table and acquire lock',
        'Employee PIN confirmation',
        'Create table assignment',
        'Process order with table context',
        'Complete payment and update table status'
      ],
      status: 'pending'
    },
    {
      id: 'concurrent-access',
      name: 'Concurrent Access Prevention',
      description: 'Test that multiple employees cannot select the same table simultaneously',
      steps: [
        'Employee A selects table',
        'Employee B attempts same table',
        'Verify Employee B is blocked',
        'Employee A completes assignment',
        'Table becomes available for Employee B'
      ],
      status: 'pending'
    },
    {
      id: 'session-management',
      name: 'Session Management',
      description: 'Test employee session tracking and table assignment persistence',
      steps: [
        'Create employee session',
        'Assign table to session',
        'Verify session activity tracking',
        'Test session timeout handling',
        'Clean up expired sessions'
      ],
      status: 'pending'
    },
    {
      id: 'real-time-sync',
      name: 'Real-Time Synchronization',
      description: 'Test WebSocket events for table status updates across terminals',
      steps: [
        'Connect multiple WebSocket clients',
        'Update table status on one client',
        'Verify updates broadcast to all clients',
        'Test order creation events',
        'Test assignment release events'
      ],
      status: 'pending'
    }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentScenario, setCurrentScenario] = useState<string | null>(null);

  const updateScenarioStatus = (id: string, status: TestScenario['status'], error?: string, duration?: number) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id 
        ? { ...scenario, status, error, duration }
        : scenario
    ));
  };

  const runBasicWorkflowTest = async (): Promise<void> => {
    const scenarioId = 'basic-workflow';
    updateScenarioStatus(scenarioId, 'running');
    const startTime = Date.now();

    try {
      // Step 1: Verify employee authentication
      console.log('🔐 Testing employee authentication...');
      const authResponse = await apiCall('/api/employees/verify-pin', {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          pin: '1234' // Test PIN
        })
      });

      if (!authResponse.ok) {
        throw new Error('Employee authentication failed');
      }

      // Step 2: Fetch available tables
      console.log('🏢 Fetching available tables...');
      const tablesResponse = await apiCall('/api/floor/tables?status=available');
      if (!tablesResponse.ok) {
        throw new Error('Failed to fetch available tables');
      }

      const tables = await tablesResponse.json();
      if (tables.length === 0) {
        throw new Error('No available tables found');
      }

      const testTable = tables[0];
      console.log('📍 Selected test table:', testTable.number);

      // Step 3: Check table availability and acquire lock
      console.log('🔒 Testing table lock acquisition...');
      const sessionId = `test_session_${Date.now()}`;
      
      const availabilityResponse = await apiCall(`/api/floor/tables/${testTable.id}/check-availability`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId
        })
      });

      if (!availabilityResponse.ok) {
        throw new Error('Table availability check failed');
      }

      const lockResponse = await apiCall(`/api/floor/tables/${testTable.id}/lock`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId,
          duration: 30000
        })
      });

      if (!lockResponse.ok) {
        throw new Error('Failed to acquire table lock');
      }

      // Step 4: Create table assignment
      console.log('👤 Creating table assignment...');
      const assignmentResponse = await apiCall('/api/floor/table-assignments', {
        method: 'POST',
        body: JSON.stringify({
          tableId: testTable.id,
          employeeId: state.currentEmployee?.id,
          sessionId,
          assignmentType: 'dine_in',
          employeeConfirmationPin: '1234',
          guestCount: 2
        })
      });

      if (!assignmentResponse.ok) {
        throw new Error('Failed to create table assignment');
      }

      // Step 5: Update table status to occupied
      console.log('🍽️ Updating table status...');
      const statusResponse = await apiCall(`/api/floor/tables/${testTable.id}/status`, {
        method: 'PUT',
        body: JSON.stringify({
          status: 'occupied',
          substatus: 'eating',
          currentOrderId: `test_order_${Date.now()}`,
          orderTotal: 25.50,
          orderItems: 3,
          guestCount: 2
        })
      });

      if (!statusResponse.ok) {
        throw new Error('Failed to update table status');
      }

      const duration = Date.now() - startTime;
      updateScenarioStatus(scenarioId, 'passed', undefined, duration);
      console.log('✅ Basic workflow test completed successfully');

    } catch (error) {
      const duration = Date.now() - startTime;
      updateScenarioStatus(scenarioId, 'failed', error instanceof Error ? error.message : 'Unknown error', duration);
      console.error('❌ Basic workflow test failed:', error);
    }
  };

  const runConcurrentAccessTest = async (): Promise<void> => {
    const scenarioId = 'concurrent-access';
    updateScenarioStatus(scenarioId, 'running');
    const startTime = Date.now();

    try {
      // Get available tables
      const tablesResponse = await apiCall('/api/floor/tables?status=available');
      const tables = await tablesResponse.json();
      
      if (tables.length === 0) {
        throw new Error('No available tables for concurrent access test');
      }

      const testTable = tables[0];
      const sessionA = `session_a_${Date.now()}`;
      const sessionB = `session_b_${Date.now()}`;

      // Employee A locks the table
      console.log('🔒 Employee A acquiring lock...');
      const lockAResponse = await apiCall(`/api/floor/tables/${testTable.id}/lock`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId: sessionA,
          duration: 30000
        })
      });

      if (!lockAResponse.ok) {
        throw new Error('Employee A failed to acquire lock');
      }

      // Employee B attempts to lock the same table
      console.log('🚫 Employee B attempting to acquire same lock...');
      const lockBResponse = await apiCall(`/api/floor/tables/${testTable.id}/lock`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId: sessionB,
          duration: 30000
        })
      });

      // This should fail (409 Conflict)
      if (lockBResponse.ok) {
        throw new Error('Employee B should not have been able to acquire lock');
      }

      if (lockBResponse.status !== 409) {
        throw new Error(`Expected 409 Conflict, got ${lockBResponse.status}`);
      }

      // Release Employee A's lock
      console.log('🔓 Releasing Employee A lock...');
      const unlockResponse = await apiCall(`/api/floor/tables/${testTable.id}/unlock`, {
        method: 'POST',
        body: JSON.stringify({ sessionId: sessionA })
      });

      if (!unlockResponse.ok) {
        throw new Error('Failed to release Employee A lock');
      }

      // Now Employee B should be able to acquire the lock
      console.log('✅ Employee B acquiring lock after release...');
      const lockB2Response = await apiCall(`/api/floor/tables/${testTable.id}/lock`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId: sessionB,
          duration: 30000
        })
      });

      if (!lockB2Response.ok) {
        throw new Error('Employee B should have been able to acquire lock after release');
      }

      const duration = Date.now() - startTime;
      updateScenarioStatus(scenarioId, 'passed', undefined, duration);
      console.log('✅ Concurrent access test completed successfully');

    } catch (error) {
      const duration = Date.now() - startTime;
      updateScenarioStatus(scenarioId, 'failed', error instanceof Error ? error.message : 'Unknown error', duration);
      console.error('❌ Concurrent access test failed:', error);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    try {
      await runBasicWorkflowTest();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Brief pause between tests
      
      await runConcurrentAccessTest();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Additional tests would go here...
      
    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setIsRunning(false);
      setCurrentScenario(null);
    }
  };

  const resetTests = () => {
    setScenarios(prev => prev.map(scenario => ({
      ...scenario,
      status: 'pending',
      error: undefined,
      duration: undefined
    })));
  };

  const getStatusIcon = (status: TestScenario['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'running':
        return <Clock className="w-5 h-5 text-blue-600 animate-spin" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestScenario['status']) => {
    switch (status) {
      case 'passed':
        return 'bg-green-50 border-green-200';
      case 'failed':
        return 'bg-red-50 border-red-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-lg">
          <h2 className="text-2xl font-bold mb-2">Dine-In Workflow Integration Tester</h2>
          <p className="text-blue-100">
            Comprehensive testing suite for POS + Floor Layout unified workflow
          </p>
        </div>

        <div className="p-6">
          {/* Test Controls */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={runAllTests}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>{isRunning ? 'Running Tests...' : 'Run All Tests'}</span>
              </button>

              <button
                onClick={resetTests}
                disabled={isRunning}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Reset</span>
              </button>
            </div>

            <div className="text-sm text-gray-600">
              Employee: {state.currentEmployee?.name || 'Not logged in'}
            </div>
          </div>

          {/* Test Scenarios */}
          <div className="space-y-4">
            {scenarios.map((scenario) => (
              <div
                key={scenario.id}
                className={`border rounded-lg p-4 transition-colors ${getStatusColor(scenario.status)}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(scenario.status)}
                    <div>
                      <h3 className="font-semibold text-gray-900">{scenario.name}</h3>
                      <p className="text-sm text-gray-600">{scenario.description}</p>
                    </div>
                  </div>
                  
                  {scenario.duration && (
                    <div className="text-sm text-gray-500">
                      {scenario.duration}ms
                    </div>
                  )}
                </div>

                {scenario.error && (
                  <div className="bg-red-100 border border-red-200 rounded p-3 mb-3">
                    <p className="text-sm text-red-700 font-medium">Error:</p>
                    <p className="text-sm text-red-600">{scenario.error}</p>
                  </div>
                )}

                <div className="space-y-1">
                  {scenario.steps.map((step, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm">
                      <div className="w-4 h-4 rounded-full bg-gray-300 flex items-center justify-center text-xs text-white">
                        {index + 1}
                      </div>
                      <span className="text-gray-700">{step}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Test Summary */}
          <div className="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Test Summary</h4>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-gray-600">
                  {scenarios.filter(s => s.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-500">Pending</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {scenarios.filter(s => s.status === 'running').length}
                </div>
                <div className="text-sm text-gray-500">Running</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {scenarios.filter(s => s.status === 'passed').length}
                </div>
                <div className="text-sm text-gray-500">Passed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {scenarios.filter(s => s.status === 'failed').length}
                </div>
                <div className="text-sm text-gray-500">Failed</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DineInWorkflowTester;
