import React from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import SuperAdminSystem from './SuperAdminSystem';
import './index.css';
import './styles/enhanced-pos.css';

// Enhanced error handling for Super Admin
window.addEventListener('error', (event) => {
  console.error('🚨 Super Admin Global Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Super Admin Unhandled Promise Rejection:', event.reason);
});

// Performance monitoring
const startTime = performance.now();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SuperAdminSystem />
  </React.StrictMode>
);

// Log performance metrics
window.addEventListener('load', () => {
  const loadTime = performance.now() - startTime;
  console.log(`🚀 Super Admin Dashboard loaded in ${loadTime.toFixed(2)}ms`);
});
