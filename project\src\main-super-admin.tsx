import React from 'react';
import ReactDOM from 'react-dom/client';
import SuperAdminSystem from './SuperAdminSystem';
import './index.css';
import './styles/enhanced-pos.css';
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      padding: '20px'
    }}>
      <div style={{
        textAlign: 'center',
        padding: '40px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '20px',
        backdropFilter: 'blur(10px)',
        border: '2px solid rgba(255, 255, 255, 0.2)',
        maxWidth: '600px'
      }}>
        <h1 style={{ fontSize: '48px', marginBottom: '20px' }}>🔒 SUPER ADMIN TEST</h1>
        <h2 style={{ fontSize: '24px', marginBottom: '30px', color: '#fecaca' }}>
          Super Admin Route Working!
        </h2>

        <div style={{ marginBottom: '30px', textAlign: 'left' }}>
          <h3 style={{ fontSize: '20px', marginBottom: '15px', color: '#fca5a5' }}>✅ Verification Status:</h3>
          <p style={{ fontSize: '16px', marginBottom: '10px' }}>✅ super-admin.html loaded correctly</p>
          <p style={{ fontSize: '16px', marginBottom: '10px' }}>✅ main-super-admin.tsx executed</p>
          <p style={{ fontSize: '16px', marginBottom: '10px' }}>✅ React component rendered</p>
          <p style={{ fontSize: '16px', marginBottom: '10px' }}>✅ Vite proxy working</p>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '20px',
          borderRadius: '10px',
          marginBottom: '30px',
          textAlign: 'left'
        }}>
          <h3 style={{ marginBottom: '15px', color: '#fca5a5' }}>🔍 Debug Information:</h3>
          <p style={{ fontSize: '14px', marginBottom: '5px' }}><strong>URL:</strong> {window.location.href}</p>
          <p style={{ fontSize: '14px', marginBottom: '5px' }}><strong>Timestamp:</strong> {new Date().toISOString()}</p>
          <p style={{ fontSize: '14px', marginBottom: '5px' }}><strong>Component:</strong> SuperAdminTest</p>
          <p style={{ fontSize: '14px', marginBottom: '5px' }}><strong>Port:</strong> {window.location.port}</p>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <button
            onClick={() => {
              console.log('🔄 Testing API connection...');
              fetch('/api/admin/health')
                .then(res => res.json())
                .then(data => {
                  console.log('✅ API Response:', data);
                  alert('API Connection: ' + JSON.stringify(data, null, 2));
                })
                .catch(err => {
                  console.error('❌ API Error:', err);
                  alert('API Error: ' + err.message);
                });
            }}
            style={{
              background: 'linear-gradient(45deg, #ef4444, #dc2626)',
              color: 'white',
              border: 'none',
              padding: '15px 30px',
              borderRadius: '10px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer',
              marginRight: '15px'
            }}
          >
            🔗 Test API Connection
          </button>

          <button
            onClick={() => {
              console.log('🔄 Loading actual Super Admin System...');
              window.location.reload();
            }}
            style={{
              background: 'linear-gradient(45deg, #059669, #047857)',
              color: 'white',
              border: 'none',
              padding: '15px 30px',
              borderRadius: '10px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}
          >
            🚀 Load Super Admin
          </button>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.05)',
          padding: '15px',
          borderRadius: '10px',
          fontSize: '14px',
          color: '#fca5a5'
        }}>
          <p><strong>Next Step:</strong> Click "Load Super Admin" to load the actual dashboard</p>
          <p><strong>Expected:</strong> Should show Super Admin login interface</p>
        </div>
      </div>
    </div>
  );
};

// Enhanced error handling for Super Admin
window.addEventListener('error', (event) => {
  console.error('🚨 Super Admin Global Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Super Admin Unhandled Promise Rejection:', event.reason);
});

// Performance monitoring
const startTime = performance.now();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SuperAdminTest />
  </React.StrictMode>
);

// Log performance metrics
window.addEventListener('load', () => {
  const loadTime = performance.now() - startTime;
  console.log(`🚀 Super Admin Dashboard loaded in ${loadTime.toFixed(2)}ms`);
});
