import React, { useState, useEffect } from 'react';
import { 
  Users, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Coffee, 
  Utensils, 
  CreditCard,
  Search,
  Filter,
  Plus,
  Settings,
  BarChart3,
  RefreshCw,
  Play,
  Pause,
  Square
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import UnifiedDineInWorkflowManager from './UnifiedDineInWorkflowManager';
import TableSelectionModal from './TableSelectionModal';
import EmployeeVerificationModal from './EmployeeVerificationModal';

interface Table {
  id: string;
  number: number;
  name?: string;
  seats: number;
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  section: string;
  guestCount?: number;
  serverName?: string;
  seatedTime?: Date;
  currentOrderId?: string;
  orderTotal?: number;
  orderItems?: number;
}

const DineInManagementInterface: React.FC = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [activeView, setActiveView] = useState<'dashboard' | 'workflow' | 'tables' | 'analytics'>('dashboard');
  const [showWorkflowManager, setShowWorkflowManager] = useState(false);
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [showEmployeeVerification, setShowEmployeeVerification] = useState(false);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLiveMode, setIsLiveMode] = useState(true);

  // Mock table data for demonstration
  const [tables] = useState<Table[]>([
    { id: '1', number: 1, seats: 4, status: 'available', section: 'Main Dining' },
    { id: '2', number: 2, seats: 2, status: 'occupied', substatus: 'eating', section: 'Bar Area', guestCount: 2, serverName: 'Alice', orderTotal: 45.50, orderItems: 3 },
    { id: '3', number: 3, seats: 6, status: 'occupied', substatus: 'ordering', section: 'Patio', guestCount: 4, serverName: 'Bob' },
    { id: '4', number: 4, seats: 4, status: 'reserved', section: 'Main Dining' },
    { id: '5', number: 5, seats: 8, status: 'available', section: 'Private Room' },
    { id: '6', number: 6, seats: 2, status: 'needs-cleaning', section: 'Bar Area' },
    { id: '7', number: 7, seats: 4, status: 'occupied', substatus: 'waiting-for-check', section: 'Main Dining', guestCount: 3, serverName: 'Carol', orderTotal: 78.25, orderItems: 5 },
    { id: '8', number: 8, seats: 6, status: 'available', section: 'Patio' }
  ]);

  const getStatusColor = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300 text-green-800';
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return 'bg-yellow-100 border-yellow-300 text-yellow-800';
          case 'eating':
            return 'bg-blue-100 border-blue-300 text-blue-800';
          case 'waiting-for-check':
            return 'bg-purple-100 border-purple-300 text-purple-800';
          case 'paying':
            return 'bg-orange-100 border-orange-300 text-orange-800';
          default:
            return 'bg-red-100 border-red-300 text-red-800';
        }
      case 'reserved':
        return 'bg-indigo-100 border-indigo-300 text-indigo-800';
      case 'needs-cleaning':
        return 'bg-gray-100 border-gray-300 text-gray-800';
      case 'out-of-order':
        return 'bg-red-200 border-red-400 text-red-900';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getStatusIcon = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return <Utensils className="h-4 w-4" />;
          case 'eating':
            return <Coffee className="h-4 w-4" />;
          case 'waiting-for-check':
            return <Clock className="h-4 w-4" />;
          case 'paying':
            return <CreditCard className="h-4 w-4" />;
          default:
            return <Users className="h-4 w-4" />;
        }
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      case 'out-of-order':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleStartWorkflow = () => {
    setShowWorkflowManager(true);
  };

  const handleTableSelect = (table: Table) => {
    setSelectedTable(table);
    if (table.status === 'available') {
      setShowEmployeeVerification(true);
    }
  };

  const handleEmployeeVerified = () => {
    setShowEmployeeVerification(false);
    setShowWorkflowManager(true);
  };

  const filteredTables = tables.filter(table => {
    const matchesSearch = !searchTerm || 
      table.number.toString().includes(searchTerm) ||
      table.section.toLowerCase().includes(searchTerm.toLowerCase()) ||
      table.serverName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || table.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const tableStats = {
    total: tables.length,
    available: tables.filter(t => t.status === 'available').length,
    occupied: tables.filter(t => t.status === 'occupied').length,
    reserved: tables.filter(t => t.status === 'reserved').length,
    needsCleaning: tables.filter(t => t.status === 'needs-cleaning').length
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available Tables</p>
              <p className="text-3xl font-bold text-green-600">{tableStats.available}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Occupied Tables</p>
              <p className="text-3xl font-bold text-blue-600">{tableStats.occupied}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Reserved Tables</p>
              <p className="text-3xl font-bold text-indigo-600">{tableStats.reserved}</p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-full">
              <Clock className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Need Cleaning</p>
              <p className="text-3xl font-bold text-orange-600">{tableStats.needsCleaning}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <AlertCircle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Table 5 became available</p>
              <p className="text-xs text-gray-500">2 minutes ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
            <Users className="w-5 h-5 text-blue-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Table 2 seated 2 guests</p>
              <p className="text-xs text-gray-500">5 minutes ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
            <Clock className="w-5 h-5 text-purple-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Table 7 requested check</p>
              <p className="text-xs text-gray-500">8 minutes ago</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleStartWorkflow}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2"
          >
            <Play className="w-5 h-5" />
            <span>Start Dine-In Workflow</span>
          </button>
          
          <button
            onClick={() => setShowTableSelector(true)}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2"
          >
            <MapPin className="w-5 h-5" />
            <span>Select Table</span>
          </button>
          
          <button
            onClick={() => setActiveView('tables')}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2"
          >
            <Settings className="w-5 h-5" />
            <span>Manage Tables</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderTablesView = () => (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search tables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="occupied">Occupied</option>
              <option value="reserved">Reserved</option>
              <option value="needs-cleaning">Needs Cleaning</option>
            </select>
          </div>

          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Add Table</span>
          </button>
        </div>
      </div>

      {/* Tables Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredTables.map((table) => (
          <div
            key={table.id}
            onClick={() => handleTableSelect(table)}
            className={`bg-white rounded-xl shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${getStatusColor(table.status, table.substatus)}`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                {getStatusIcon(table.status, table.substatus)}
                <h3 className="text-lg font-bold">Table {table.number}</h3>
              </div>
              <div className="text-sm font-medium capitalize">
                {table.substatus || table.status}
              </div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Section:</span>
                <span className="font-medium">{table.section}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Seats:</span>
                <span className="font-medium">{table.seats}</span>
              </div>
              {table.guestCount && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Guests:</span>
                  <span className="font-medium">{table.guestCount}</span>
                </div>
              )}
              {table.serverName && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Server:</span>
                  <span className="font-medium">{table.serverName}</span>
                </div>
              )}
              {table.orderTotal && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Total:</span>
                  <span className="font-medium">${table.orderTotal.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalyticsView = () => (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Table Turnover</h3>
          <div className="text-3xl font-bold text-blue-600 mb-2">2.3</div>
          <p className="text-sm text-gray-600">Average turns per table today</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Average Wait Time</h3>
          <div className="text-3xl font-bold text-green-600 mb-2">12m</div>
          <p className="text-sm text-gray-600">From seating to first order</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Efficiency</h3>
          <div className="text-3xl font-bold text-purple-600 mb-2">94%</div>
          <p className="text-sm text-gray-600">On-time service delivery</p>
        </div>
      </div>

      {/* Section Performance */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Section Performance</h3>
        <div className="space-y-4">
          {['Main Dining', 'Bar Area', 'Patio', 'Private Room'].map((section) => (
            <div key={section} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-medium">{section}</span>
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-bold text-gray-900">85%</div>
                  <div className="text-gray-500">Occupancy</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-gray-900">$45</div>
                  <div className="text-gray-500">Avg Check</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-gray-900">1.8</div>
                  <div className="text-gray-500">Turnover</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
                <Utensils className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dine-In Management</h1>
                <p className="text-sm text-gray-600">Unified workflow interface for table service</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isLiveMode ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
              <span className="text-sm font-medium text-gray-700">
                {isLiveMode ? 'Live Mode' : 'Offline'}
              </span>
            </div>
            
            <button
              onClick={() => setIsLiveMode(!isLiveMode)}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 px-6">
        <div className="flex space-x-8">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
            { id: 'workflow', label: 'Workflow', icon: Play },
            { id: 'tables', label: 'Tables', icon: MapPin },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map((tab) => {
            const Icon = tab.icon;
            const isActive = activeView === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeView === 'dashboard' && renderDashboard()}
        {activeView === 'workflow' && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Management</h3>
            <p className="text-gray-600 mb-6">Manage and monitor dine-in workflows in real-time</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-blue-900 mb-2">Complete Workflow</h4>
                <p className="text-blue-700 text-sm mb-4">Full table selection to order completion</p>
                <button
                  onClick={handleStartWorkflow}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 w-full"
                >
                  Start Complete Workflow
                </button>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-green-900 mb-2">Quick Table Selection</h4>
                <p className="text-green-700 text-sm mb-4">Select table for existing order</p>
                <button
                  onClick={() => setShowTableSelector(true)}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 w-full"
                >
                  Select Table Only
                </button>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Workflow Status</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">12</div>
                  <div className="text-sm text-gray-600">Active Workflows</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">45</div>
                  <div className="text-sm text-gray-600">Completed Today</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">98%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
              </div>
            </div>
          </div>
        )}
        {activeView === 'tables' && renderTablesView()}
        {activeView === 'analytics' && renderAnalyticsView()}
      </div>

      {/* Modals */}
      {showWorkflowManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
            <UnifiedDineInWorkflowManager
              onWorkflowComplete={() => setShowWorkflowManager(false)}
              onCancel={() => setShowWorkflowManager(false)}
            />
          </div>
        </div>
      )}

      {showTableSelector && (
        <TableSelectionModal
          isOpen={showTableSelector}
          onClose={() => setShowTableSelector(false)}
          onTableSelect={handleTableSelect}
        />
      )}

      {showEmployeeVerification && selectedTable && (
        <EmployeeVerificationModal
          isOpen={showEmployeeVerification}
          onClose={() => setShowEmployeeVerification(false)}
          onVerified={handleEmployeeVerified}
          tableInfo={{
            tableNumber: selectedTable.number,
            section: selectedTable.section,
            seats: selectedTable.seats
          }}
        />
      )}
    </div>
  );
};

export default DineInManagementInterface;
