<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enterprise Super Admin Dashboard - Multi-Tenant POS Management</title>
    <meta name="description" content="Advanced Super Administrator Dashboard for Multi-Tenant Restaurant POS System Management with Real-time Analytics and AI-Powered Insights" />
    <meta name="keywords" content="super admin, multi-tenant, pos management, restaurant analytics, enterprise dashboard, admin panel" />

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Favicon and app icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Performance and SEO optimizations -->
    <meta name="theme-color" content="#dc2626" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Critical CSS for loading state -->
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%);
        min-height: 100vh;
        overflow-x: hidden;
        line-height: 1.5;
      }

      #root {
        min-height: 100vh;
        position: relative;
      }

      /* Enhanced Loading Screen */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.2);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 24px;
      }

      .loading-title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
      }

      .loading-subtitle {
        font-size: 16px;
        font-weight: 400;
        opacity: 0.8;
        margin-bottom: 24px;
      }

      .loading-progress {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin: 0 auto;
      }

      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #ffffff, #fecaca);
        border-radius: 2px;
        animation: progress 2s ease-in-out infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @keyframes progress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
      }



      /* Hide loading when app loads */
      .app-loaded .loading-container {
        display: none;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .loading-title {
          font-size: 20px;
        }

        .loading-subtitle {
          font-size: 14px;
        }


      }
    </style>
  </head>
  <body>


    <!-- Enhanced Loading Screen -->
    <div class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-title">Enterprise Super Admin</div>
        <div class="loading-subtitle">Loading Multi-Tenant Management Dashboard...</div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- Main app container -->
    <div id="root"></div>

    <!-- Main application script -->
    <script type="module" src="/src/main-super-admin.tsx"></script>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
  </body>
</html>
