import React, { useState, useEffect, Component, ErrorInfo, ReactNode } from 'react';
import { TenantProvider } from './context/TenantContext';
import { EnhancedAppProvider } from './context/EnhancedAppContext';
import { ComprehensiveAdminDashboard } from './pages/ComprehensiveAdminDashboard';

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    console.error('💥 ErrorBoundary: Caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('💥 ErrorBoundary: Error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-500 text-white p-8">
          <h1 className="text-4xl font-bold mb-4">💥 APPLICATION ERROR</h1>
          <div className="bg-black bg-opacity-50 p-4 rounded-lg">
            <h2 className="text-xl font-bold mb-2">Error Details:</h2>
            <p className="mb-2">{this.state.error?.message}</p>
            <pre className="text-sm overflow-auto">{this.state.error?.stack}</pre>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Super Admin Interface Component
const SuperAdminInterface: React.FC = () => {
  return <ComprehensiveAdminDashboard />;
};

// Enhanced Super Admin Login Component
const SuperAdminLogin: React.FC<{ onLogin: (success: boolean) => void }> = ({ onLogin }) => {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');

  // Handle PIN input
  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    console.log('🔐 Super Admin PIN validation:', pin);

    try {
      // Authenticate with main backend API (port 4000)
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin
          // Let backend auto-detect tenant
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // Check if user has super admin role
        if (data.employee && data.employee.role === 'super_admin') {
          console.log('✅ Super Admin authenticated successfully');

          // Store the JWT token in localStorage using consistent key
          localStorage.setItem('authToken', data.token);
          localStorage.setItem('employee', JSON.stringify(data.employee));
          localStorage.setItem('tenant', JSON.stringify(data.tenant));

          setError('');
          onLogin(true);
        } else {
          console.log('❌ User is not a super admin');
          setError('Access denied. Super Admin privileges required.');
          setPin('');
        }
      } else {
        console.log('❌ Authentication failed');
        setError('Invalid PIN. Please try again.');
        setPin('');
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('Login failed. Please try again.');
      setPin('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-pink-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-red-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-rose-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      {/* Super Admin Status Indicator */}
      <div className="fixed top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-lg font-bold text-sm shadow-lg z-50 animate-pulse">
        🔒 SUPER ADMIN MODE
      </div>

      {/* Version Info */}
      <div className="fixed top-4 right-4 text-white/70 text-xs font-medium z-50">
        v2.0.0 Enterprise Admin
      </div>

      <div className="max-w-md w-full relative z-10">
        <div className="text-center mb-8">
          <div className="mx-auto w-20 h-20 bg-gradient-to-r from-red-600 to-pink-600 rounded-full flex items-center justify-center mb-6 shadow-2xl">
            <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-white mb-3">Super Admin Portal</h1>
          <p className="text-red-200 text-lg mb-6">Multi-Tenant System Management</p>

          {/* Enhanced Warning Panel */}
          <div className="bg-red-800/50 backdrop-blur-sm border border-red-600/30 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-red-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-red-200 font-semibold">RESTRICTED ACCESS</span>
            </div>
            <p className="text-sm text-red-100 leading-relaxed">
              This portal provides complete administrative control over all tenant systems,
              user management, billing, and system-wide configurations. Access is logged and monitored.
            </p>
          </div>

          {/* System Status */}
          <div className="grid grid-cols-2 gap-3 mb-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-xs text-red-200 mb-1">System Status</div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-white font-medium text-sm">Operational</span>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-xs text-red-200 mb-1">Security Level</div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
                <span className="text-white font-medium text-sm">Maximum</span>
              </div>
            </div>
          </div>
        </div>

        {/* Simple Super Admin PIN Input */}
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-grey-900mb-4">PIN</h2>

            {/* PIN Display */}
            <div className="flex justify-center space-x-3 mb-6">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className={`w-12 h-12 border-2 rounded-xl flex items-center justify-center text-2xl font-bold transition-all duration-200 ${
                    i < pin.length
                      ? 'border-red-500 bg-red-50 text-red-600 shadow-md scale-105'
                      : 'border-gray-300 bg-gray-50 text-gray-400'
                  }`}
                >
                  {i < pin.length ? '●' : '○'}
                </div>
              ))}
            </div>

            {/* Number Pad */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
                <button
                  key={digit}
                  type="button"
                  onClick={() => handlePinInput(digit.toString())}
                  className="h-14 bg-gray-100 hover:bg-gray-200 active:bg-gray-300 rounded-xl text-xl font-semibold text-gray-700 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
                  data-testid={`pin-button-${digit}`}
                >
                  {digit}
                </button>
              ))}

              <button
                type="button"
                onClick={() => setPin('')}
                className="h-14 bg-red-100 hover:bg-red-200 active:bg-red-300 rounded-xl text-sm font-semibold text-red-600 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
              >
                Clear
              </button>

              <button
                type="button"
                onClick={() => handlePinInput('0')}
                className="h-14 bg-gray-100 hover:bg-gray-200 active:bg-gray-300 rounded-xl text-xl font-semibold text-gray-700 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
                data-testid="pin-button-0"
              >
                0
              </button>

              <button
                type="button"
                onClick={() => setPin(pin.slice(0, -1))}
                className="h-14 bg-yellow-100 hover:bg-yellow-200 active:bg-yellow-300 rounded-xl text-lg font-semibold text-yellow-600 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md flex items-center justify-center"
              >
                ⌫
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}

            {/* Sign In Button */}
            <button
              type="button"
              onClick={handleSubmit}
              disabled={pin.length !== 6}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 transform ${
                pin.length !== 6
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95'
              }`}
              data-testid="sign-in-button"
            >
              Sign In
            </button>

            {/* Test Credentials */}
            <div className="mt-4 p-3 bg-grey-900 rounded-lg text-sm">
              <p className="font-semibold text-red-800 mb-1">Super Admin PINs:</p>
              <p className="text-red-700"> NOT AVAILABLE </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 text-white/60 text-sm text-center z-50">
        <p>Enterprise Multi-Tenant POS Administration</p>
        <p className="text-xs mt-1">© 2024 Restaurant POS. All rights reserved.</p>
      </div>
    </div>
  );
};

// Main Super Admin Content Component
const SuperAdminContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  console.log('🔐 SuperAdminContent: isLoggedIn =', isLoggedIn);

  if (!isLoggedIn) {
    console.log('🔐 SuperAdminContent: Showing login screen');
    return <SuperAdminLogin onLogin={setIsLoggedIn} />;
  }

  console.log('🔐 SuperAdminContent: Showing admin interface');
  return <SuperAdminInterface />;
};

// Main Super Admin System Component
const SuperAdminSystem: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Debug logging
  console.log('🔐 SuperAdminSystem: isLoggedIn =', isLoggedIn);

  // Check for any auto-login conditions and prevent them
  React.useEffect(() => {
    // Force logout on component mount to ensure login screen shows
    setIsLoggedIn(false);
    console.log('🔐 SuperAdminSystem: Forced logout on mount');
  }, []);

  return (
    <ErrorBoundary>
      <EnhancedAppProvider>
        <TenantProvider>
          <SuperAdminContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
        </TenantProvider>
      </EnhancedAppProvider>
    </ErrorBoundary>
  );
};

export default SuperAdminSystem;
