import React, { useState, useEffect, Component, ErrorInfo, ReactNode } from 'react';
import { TenantProvider } from './context/TenantContext';
import { EnhancedAppProvider } from './context/EnhancedAppContext';
import { ComprehensiveAdminDashboard } from './pages/ComprehensiveAdminDashboard';

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    console.error('💥 ErrorBoundary: Caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('💥 ErrorBoundary: Error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-500 text-white p-8">
          <h1 className="text-4xl font-bold mb-4">💥 APPLICATION ERROR</h1>
          <div className="bg-black bg-opacity-50 p-4 rounded-lg">
            <h2 className="text-xl font-bold mb-2">Error Details:</h2>
            <p className="mb-2">{this.state.error?.message}</p>
            <pre className="text-sm overflow-auto">{this.state.error?.stack}</pre>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Super Admin Interface Component
const SuperAdminInterface: React.FC = () => {
  return <ComprehensiveAdminDashboard />;
};

// Enhanced Super Admin Login Component
const SuperAdminLogin: React.FC<{ onLogin: (success: boolean) => void }> = ({ onLogin }) => {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPin, setShowPin] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check for dark mode preference
  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);

    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  // Handle PIN input
  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!pin.trim()) {
      setError('Please enter your Super Admin PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Authenticate with main backend API (port 4000)
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin
          // Let backend auto-detect tenant
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // Check if user has super admin role
        if (data.employee && data.employee.role === 'super_admin') {
          console.log('✅ Super Admin authenticated successfully');

          // Store the JWT token in localStorage using consistent key
          localStorage.setItem('authToken', data.token);
          localStorage.setItem('employee', JSON.stringify(data.employee));
          localStorage.setItem('tenant', JSON.stringify(data.tenant));

          setError('');
          onLogin(true);
        } else {
          setError(`Access Denied: Super Administrator privileges required. Current role: ${data.employee?.role || 'unknown'}`);
          setPin('');
        }
      } else {
        setError('Invalid Super Admin PIN. Access denied.');
        setPin('');
      }
    } catch (error) {
      console.error('Super Admin login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Connection failed. Please ensure the backend server is running.');
      } else {
        setError('Authentication failed. Please verify your Super Admin PIN.');
      }
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`min-h-screen w-full flex items-center justify-center p-4 relative overflow-hidden transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gradient-to-br from-gray-900 via-red-900 to-purple-900'
        : 'bg-gradient-to-br from-red-50 via-pink-50 to-purple-50'
    }`}>

      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className={`fixed top-4 right-4 p-3 rounded-full transition-all duration-300 z-50 ${
          isDarkMode
            ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700'
            : 'bg-white text-gray-600 hover:bg-gray-100'
        } shadow-lg hover:shadow-xl`}
      >
        {isDarkMode ? '☀️' : '🌙'}
      </button>

      {/* Security Badge */}
      <div className={`fixed top-4 left-4 px-4 py-2 rounded-lg font-bold text-sm shadow-lg z-50 ${
        isDarkMode
          ? 'bg-red-600 text-white'
          : 'bg-red-500 text-white'
      }`}>
        🔒 RESTRICTED ACCESS
      </div>

      {/* Main Login Card */}
      <div className={`w-full max-w-md relative z-10 transition-all duration-300 ${
        isDarkMode
          ? 'bg-gray-800/95 border-red-700'
          : 'bg-white/95 border-red-200'
      } backdrop-blur-xl rounded-2xl shadow-2xl border-2 p-8`}>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-r from-red-500 to-pink-600 flex items-center justify-center shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>

          <h1 className={`text-3xl font-bold mb-2 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Super Admin
          </h1>

          <p className={`text-sm font-medium ${
            isDarkMode ? 'text-red-300' : 'text-red-600'
          }`}>
            System Administration Portal
          </p>


          <div className={`mt-4 p-3 rounded-lg ${
            isDarkMode
              ? 'bg-red-900/30 border border-red-700'
              : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-xs ${
              isDarkMode ? 'text-red-300' : 'text-red-700'
            }`}>
              ⚠️ Authorized Personnel Only
            </p>
          </div>
        </div>

        {/* Admin Features Preview */}
        <div className="mb-6">
          <div className="grid grid-cols-2 gap-3">
            <div className={`p-3 rounded-lg text-center ${
              isDarkMode
                ? 'bg-gray-700/50 border border-gray-600'
                : 'bg-gray-50 border border-gray-200'
            }`}>
              <svg className={`w-6 h-6 mx-auto mb-1 ${
                isDarkMode ? 'text-blue-400' : 'text-blue-600'
              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <p className={`text-xs font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                User Management
              </p>
            </div>

            <div className={`p-3 rounded-lg text-center ${
              isDarkMode
                ? 'bg-gray-700/50 border border-gray-600'
                : 'bg-gray-50 border border-gray-200'
            }`}>
              <svg className={`w-6 h-6 mx-auto mb-1 ${
                isDarkMode ? 'text-green-400' : 'text-green-600'
              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
              <p className={`text-xs font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                System Data
              </p>
            </div>

            <div className={`p-3 rounded-lg text-center ${
              isDarkMode
                ? 'bg-gray-700/50 border border-gray-600'
                : 'bg-gray-50 border border-gray-200'
            }`}>
              <svg className={`w-6 h-6 mx-auto mb-1 ${
                isDarkMode ? 'text-purple-400' : 'text-purple-600'
              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className={`text-xs font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Analytics
              </p>
            </div>

            <div className={`p-3 rounded-lg text-center ${
              isDarkMode
                ? 'bg-gray-700/50 border border-gray-600'
                : 'bg-gray-50 border border-gray-200'
            }`}>
              <svg className={`w-6 h-6 mx-auto mb-1 ${
                isDarkMode ? 'text-orange-400' : 'text-orange-600'
              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <p className={`text-xs font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Configuration
              </p>
            </div>
          </div>
        </div>

        {/* PIN Input Section */}
        <div>
          <label className={`block text-sm font-medium mb-3 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            Super Admin PIN
          </label>

          <div className="relative mb-6">
            <input
              type={showPin ? 'text' : 'password'}
              value={pin}
              onChange={(e) => setPin(e.target.value.slice(0, 6))}
              className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 text-center text-lg font-mono tracking-widest ${
                isDarkMode
                  ? 'bg-gray-700 border-red-600 text-white focus:border-red-400'
                  : 'bg-gray-50 border-red-300 text-gray-900 focus:border-red-500'
              } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
              placeholder="••••••"
              maxLength={6}
              autoComplete="off"
            />

            <button
              type="button"
              onClick={() => setShowPin(!showPin)}
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${
                isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'
              } transition-colors`}
            >
              {showPin ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>

          {/* Number Pad */}
          <div className="grid grid-cols-3 gap-3 mb-6">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
              <button
                key={digit}
                type="button"
                onClick={() => handlePinInput(digit.toString())}
                disabled={isLoading}
                className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
                } hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {digit}
              </button>
            ))}

            <button
              type="button"
              onClick={() => setPin('')}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium text-sm transition-all duration-150 ${
                isDarkMode
                  ? 'bg-red-600 hover:bg-red-500 text-white'
                  : 'bg-red-100 hover:bg-red-200 text-red-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              Clear
            </button>

            <button
              type="button"
              onClick={() => handlePinInput('0')}
              disabled={isLoading}
              className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                isDarkMode
                  ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              0
            </button>

            <button
              type="button"
              onClick={() => setPin(pin.slice(0, -1))}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium transition-all duration-150 ${
                isDarkMode
                  ? 'bg-yellow-600 hover:bg-yellow-500 text-white'
                  : 'bg-yellow-100 hover:bg-yellow-200 text-yellow-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50 flex items-center justify-center`}
            >
              ⌫
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 mb-4 ${
              isDarkMode
                ? 'bg-red-900/50 border border-red-700 text-red-300'
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Login Button */}
          <button
            type="button"
            onClick={handleSubmit}
            disabled={pin.length === 0 || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              pin.length === 0 || isLoading
                ? isDarkMode
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl'
            } transform hover:scale-105 active:scale-95`}
          >
            {isLoading ? (
              <>
                <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Authenticating...</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span>Access Admin Dashboard</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </>
            )}
          </button>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>Super Administrator Portal</p>
            <p className="mt-1">© 2024 RestroFlow. All rights reserved.</p>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob ${
          isDarkMode ? 'bg-red-600' : 'bg-red-300'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000 ${
          isDarkMode ? 'bg-pink-600' : 'bg-pink-300'
        }`}></div>
        <div className={`absolute top-40 left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000 ${
          isDarkMode ? 'bg-purple-600' : 'bg-purple-300'
        }`}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob { animation: blob 7s infinite; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-4000 { animation-delay: 4s; }
      `}</style>
    </div>
  );
};

// Main Super Admin Content Component
const SuperAdminContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  console.log('🔐 SuperAdminContent: isLoggedIn =', isLoggedIn);

  if (!isLoggedIn) {
    console.log('🔐 SuperAdminContent: Showing login screen');
    return <SuperAdminLogin onLogin={setIsLoggedIn} />;
  }

  console.log('🔐 SuperAdminContent: Showing admin interface');
  return <SuperAdminInterface />;
};

// Main Super Admin System Component
const SuperAdminSystem: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Debug logging
  console.log('🔐 SuperAdminSystem: isLoggedIn =', isLoggedIn);

  // Check for any auto-login conditions and prevent them
  React.useEffect(() => {
    // Force logout on component mount to ensure login screen shows
    setIsLoggedIn(false);
    console.log('🔐 SuperAdminSystem: Forced logout on mount');
  }, []);

  return (
    <ErrorBoundary>
      <EnhancedAppProvider>
        <TenantProvider>
          <SuperAdminContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
        </TenantProvider>
      </EnhancedAppProvider>
    </ErrorBoundary>
  );
};

export default SuperAdminSystem;
