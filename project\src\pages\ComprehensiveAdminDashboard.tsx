import React, { useState, useEffect, useCallback } from 'react';
import {
  comprehensiveAdminApiService,
  SystemMetrics,
  Tenant,
  User,
  SystemAnalytics,
  SecurityAudit,
  AIAnalytics,
  SystemActivity
} from '../services/comprehensiveAdminApiService';

// Phase 3G: Advanced Kitchen Display System - Moved to POS System

// Phase 3H: Multi-Currency Support System
import Phase3HMultiCurrencySupport from '../components/admin/finance/Phase3HMultiCurrencySupport';

// Enhanced User Management Component
import { EnhancedUserManager } from '../components/EnhancedUserManager';

// Enhanced Tenant Management Component
import { TenantManagement } from '../components/admin/tenants/TenantManagement';

// Icons
import {
  Users,
  DollarSign,
  Activity,
  Server,
  TrendingUp,
  Clock,
  BarChart3,
  Shield,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Wifi,
  WifiOff,
  Globe,
  Bot,
  Smartphone,
  Brain,
  Target,
  Database,
  Settings,
  Bell,
  Lock,
  Zap,
  PieChart,
  LineChart,
  Calendar,
  FileText,
  Download,
  Upload,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  AlertCircle,
  Info,
  X,
  Save,
  Key,
  Crown,
  XCircle,
  User as UserIcon
} from 'lucide-react';

// Comprehensive Admin Dashboard Component
export function ComprehensiveAdminDashboard() {
  // Authentication check
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);

  // State Management
  const [currentView, setCurrentView] = useState('dashboard');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false); // Disabled by default to reduce server load

  // Data State
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [analytics, setAnalytics] = useState<SystemAnalytics | null>(null);
  const [securityAudits, setSecurityAudits] = useState<SecurityAudit[]>([]);
  const [aiAnalytics, setAIAnalytics] = useState<AIAnalytics | null>(null);
  const [activities, setActivities] = useState<SystemActivity[]>([]);

  // Database connection state
  const [databaseConnected, setDatabaseConnected] = useState(false);

  // Profile Management State
  const [profile, setProfile] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileFormData, setProfileFormData] = useState({
    name: '',
    email: ''
  });
  const [isChangingPin, setIsChangingPin] = useState(false);
  const [pinFormData, setPinFormData] = useState({
    currentPin: '',
    newPin: '',
    confirmPin: ''
  });
  const [showCurrentPin, setShowCurrentPin] = useState(false);
  const [showNewPin, setShowNewPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPinModal, setShowPinModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Tenant Management State
  const [showTenantModal, setShowTenantModal] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [tenantModalMode, setTenantModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [tenantToDelete, setTenantToDelete] = useState<Tenant | null>(null);
  const [tenantFormData, setTenantFormData] = useState({
    name: '',
    slug: '',
    email: '',
    phone: '',
    address: '',
    status: 'active' as const
  });

  // Streamlined navigation configuration without phase indicators
  const navigationConfig = {
    'dashboard': {
      label: 'Dashboard',
      icon: '📊',
      color: 'blue',
      category: 'core'
    },
    'tenant-management': {
      label: 'Tenants',
      icon: '🏢',
      color: 'green',
      category: 'core'
    },
    'user-management': {
      label: 'Users',
      icon: '👥',
      color: 'purple',
      category: 'core'
    },
    'billing-management': {
      label: 'Billing',
      icon: '💳',
      color: 'emerald',
      category: 'core'
    },
    'system-metrics': {
      label: 'Metrics',
      icon: '📈',
      color: 'indigo',
      category: 'core'
    },
    'advanced-analytics': {
      label: 'Analytics',
      icon: '📊',
      color: 'cyan',
      category: 'analytics'
    },
    'security-audit': {
      label: 'Security',
      icon: '🛡️',
      color: 'red',
      category: 'security'
    },
    'backup-management': {
      label: 'Backup',
      icon: '💾',
      color: 'gray',
      category: 'system'
    },
    'api-management': {
      label: 'API',
      icon: '🔌',
      color: 'orange',
      category: 'technical'
    },
    'performance-monitor': {
      label: 'Performance',
      icon: '⚡',
      color: 'yellow',
      category: 'monitoring'
    },
    'system-optimization': {
      label: 'Optimization',
      icon: '🔧',
      color: 'teal',
      category: 'optimization'
    },
    'ai-analytics': {
      label: 'AI Analytics',
      icon: '🤖',
      color: 'violet',
      category: 'ai'
    },
    'predictive-forecasting': {
      label: 'Forecasting',
      icon: '🔮',
      color: 'pink',
      category: 'ai'
    },
    'inventory-optimization': {
      label: 'Inventory AI',
      icon: '📦',
      color: 'lime',
      category: 'ai'
    },
    'smart-pricing': {
      label: 'Smart Pricing',
      icon: '💰',
      color: 'amber',
      category: 'ai'
    },
    'staff-optimization': {
      label: 'Staff AI',
      icon: '👨‍💼',
      color: 'rose',
      category: 'ai'
    },
    'customer-behavior': {
      label: 'Customer AI',
      icon: '🧠',
      color: 'sky',
      category: 'ai'
    },
    'multi-currency': {
      label: 'Multi-Currency',
      icon: '🌍',
      color: 'emerald',
      category: 'finance'
    }
  };

  // Fetch all dashboard data with real PostgreSQL connectivity
  const fetchDashboardData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching comprehensive dashboard data from PostgreSQL...');

      // Test database connection first
      const dbConnected = await comprehensiveAdminApiService.testDatabaseConnection();
      setDatabaseConnected(dbConnected);
      setIsOnline(dbConnected);

      if (!dbConnected) {
        throw new Error('Database connection failed. Please check PostgreSQL server.');
      }

      // Fetch all data in parallel for optimal performance
      const [
        metricsData,
        tenantsData,
        usersData,
        analyticsData,
        securityData,
        aiData,
        activitiesData
      ] = await Promise.all([
        comprehensiveAdminApiService.getSystemMetrics(forceRefresh),
        comprehensiveAdminApiService.getTenants(forceRefresh),
        comprehensiveAdminApiService.getUsers(forceRefresh),
        comprehensiveAdminApiService.getSystemAnalytics(forceRefresh),
        comprehensiveAdminApiService.getSecurityAudits(forceRefresh),
        comprehensiveAdminApiService.getAIAnalytics(forceRefresh),
        comprehensiveAdminApiService.getSystemActivity(forceRefresh)
      ]);

      // Update state with real data
      setMetrics(metricsData);
      setTenants(tenantsData);
      setUsers(usersData);
      setAnalytics(analyticsData);
      setSecurityAudits(securityData);
      setAIAnalytics(aiData);
      setActivities(activitiesData);
      setLastRefresh(new Date());

      console.log('✅ Comprehensive dashboard data loaded successfully');
      console.log('📊 System Metrics:', metricsData);
      console.log('🏢 Tenants:', tenantsData.length);
      console.log('👥 Users:', usersData.length);
      console.log('📈 Analytics:', analyticsData);
      console.log('🛡️ Security Audits:', securityData.length);
      console.log('🤖 AI Analytics:', aiData);
      console.log('📋 Activities:', activitiesData.length);

    } catch (error) {
      console.error('💥 Error fetching comprehensive dashboard data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
      setIsOnline(false);
      setDatabaseConnected(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Manual refresh function
  const handleRefresh = useCallback(() => {
    console.log('🔄 Manual refresh triggered');
    fetchDashboardData(true);
  }, [fetchDashboardData]); // fetchDashboardData is stable with useCallback

  // Tenant Management Functions
  const handleCreateTenant = () => {
    setTenantModalMode('create');
    setSelectedTenant(null);
    setTenantFormData({
      name: '',
      slug: '',
      email: '',
      phone: '',
      address: '',
      status: 'active'
    });
    setShowTenantModal(true);
  };

  const handleEditTenant = (tenant: Tenant) => {
    setTenantModalMode('edit');
    setSelectedTenant(tenant);
    setTenantFormData({
      name: tenant.name || '',
      slug: tenant.slug || '',
      email: tenant.email || '',
      phone: tenant.phone || '',
      address: tenant.address || '',
      status: tenant.status || 'active'
    });
    setShowTenantModal(true);
  };

  const handleViewTenant = (tenant: Tenant) => {
    setTenantModalMode('view');
    setSelectedTenant(tenant);
    setTenantFormData({
      name: tenant.name || '',
      slug: tenant.slug || '',
      email: tenant.email || '',
      phone: tenant.phone || '',
      address: tenant.address || '',
      status: tenant.status || 'active'
    });
    setShowTenantModal(true);
  };

  const handleDeleteTenant = (tenant: Tenant) => {
    setTenantToDelete(tenant);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteTenant = async () => {
    if (!tenantToDelete) return;

    try {
      setLoading(true);
      await comprehensiveAdminApiService.deleteTenant(tenantToDelete.id, { confirmDelete: true });

      // Refresh tenants list
      const updatedTenants = await comprehensiveAdminApiService.getTenants(true);
      setTenants(updatedTenants);

      setShowDeleteConfirm(false);
      setTenantToDelete(null);
      console.log('✅ Tenant deleted successfully');
    } catch (error) {
      console.error('💥 Error deleting tenant:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete tenant');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveTenant = async () => {
    try {
      setLoading(true);

      if (tenantModalMode === 'create') {
        await comprehensiveAdminApiService.createTenant(tenantFormData);
      } else if (tenantModalMode === 'edit' && selectedTenant) {
        await comprehensiveAdminApiService.updateTenant(selectedTenant.id, tenantFormData);
      }

      // Refresh tenants list
      const updatedTenants = await comprehensiveAdminApiService.getTenants(true);
      setTenants(updatedTenants);

      setShowTenantModal(false);
      setSelectedTenant(null);
      console.log(`✅ Tenant ${tenantModalMode === 'create' ? 'created' : 'updated'} successfully`);
    } catch (error) {
      console.error(`💥 Error ${tenantModalMode === 'create' ? 'creating' : 'updating'} tenant:`, error);
      setError(error instanceof Error ? error.message : `Failed to ${tenantModalMode} tenant`);
    } finally {
      setLoading(false);
    }
  };

  const handleActivateTenant = async (tenant: Tenant) => {
    try {
      setLoading(true);
      await comprehensiveAdminApiService.activateTenant(tenant.id);

      // Refresh tenants list
      const updatedTenants = await comprehensiveAdminApiService.getTenants(true);
      setTenants(updatedTenants);

      console.log('✅ Tenant activated successfully');
    } catch (error) {
      console.error('💥 Error activating tenant:', error);
      setError(error instanceof Error ? error.message : 'Failed to activate tenant');
    } finally {
      setLoading(false);
    }
  };

  const handleSuspendTenant = async (tenant: Tenant) => {
    try {
      setLoading(true);
      await comprehensiveAdminApiService.suspendTenant(tenant.id, { reason: 'Suspended by admin' });

      // Refresh tenants list
      const updatedTenants = await comprehensiveAdminApiService.getTenants(true);
      setTenants(updatedTenants);

      console.log('✅ Tenant suspended successfully');
    } catch (error) {
      console.error('💥 Error suspending tenant:', error);
      setError(error instanceof Error ? error.message : 'Failed to suspend tenant');
    } finally {
      setLoading(false);
    }
  };

  // Profile Management Functions
  const loadProfile = async () => {
    try {
      setProfileLoading(true);
      setProfileError(null);

      const token = localStorage.getItem('authToken');
      if (!token) {
        setProfileError('Authentication required. Please log in.');
        return;
      }

      // Get current user info from localStorage first
      const storedEmployee = localStorage.getItem('currentEmployee');
      if (storedEmployee) {
        const employee = JSON.parse(storedEmployee);
        const storedTenant = localStorage.getItem('currentTenant');
        const tenant = storedTenant ? JSON.parse(storedTenant) : null;

        const profileData = {
          id: employee.id,
          name: employee.name,
          email: '<EMAIL>', // Default email for owner
          role: employee.role,
          lastLogin: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isActive: true,
          tenantName: tenant?.name || 'Unknown',
          tenantSlug: tenant?.slug || 'unknown'
        };

        setProfile(profileData);
        setProfileFormData({
          name: profileData.name || '',
          email: profileData.email || ''
        });
      }

      // Try to fetch from API for more complete data
      try {
        const response = await fetch('http://localhost:4000/api/admin/profile', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const apiProfileData = await response.json();
          setProfile(apiProfileData);
          setProfileFormData({
            name: apiProfileData.name || '',
            email: apiProfileData.email || ''
          });
          console.log('✅ Profile loaded from API:', apiProfileData);
        } else {
          console.log('⚠️ API profile fetch failed, using localStorage data');
        }
      } catch (apiError) {
        console.log('⚠️ API profile fetch error, using localStorage data:', apiError);
      }

    } catch (error) {
      console.error('💥 Error loading profile:', error);
      setProfileError(error instanceof Error ? error.message : 'Failed to load profile');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleUpdateProfile = async () => {
    try {
      setProfileLoading(true);
      setProfileError(null);

      const token = localStorage.getItem('authToken');

      const response = await fetch(`http://localhost:4000/api/admin/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileFormData)
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setProfile(updatedProfile);
        setIsEditingProfile(false);
        console.log('✅ Profile updated successfully');
      } else {
        const errorData = await response.json();
        setProfileError(errorData.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('💥 Error updating profile:', error);
      setProfileError(error instanceof Error ? error.message : 'Failed to update profile');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleChangePin = async () => {
    try {
      if (pinFormData.newPin !== pinFormData.confirmPin) {
        setProfileError('New PIN and confirmation do not match');
        return;
      }

      if (pinFormData.newPin.length !== 6) {
        setProfileError('PIN must be exactly 6 digits');
        return;
      }

      setProfileLoading(true);
      setProfileError(null);

      const token = localStorage.getItem('authToken');

      const response = await fetch('http://localhost:4000/api/admin/profile/change-pin', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPin: pinFormData.currentPin,
          newPin: pinFormData.newPin
        })
      });

      if (response.ok) {
        setIsChangingPin(false);
        setPinFormData({ currentPin: '', newPin: '', confirmPin: '' });
        console.log('✅ PIN changed successfully');
      } else {
        const errorData = await response.json();
        setProfileError(errorData.message || 'Failed to change PIN');
      }
    } catch (error) {
      console.error('💥 Error changing PIN:', error);
      setProfileError(error instanceof Error ? error.message : 'Failed to change PIN');
    } finally {
      setProfileLoading(false);
    }
  };

  // Enhanced view change handler with loading states
  const handleViewChange = useCallback(async (newView: string) => {
    if (newView === currentView) return;

    setIsTransitioning(true);
    setError(null);

    try {
      // Simulate loading time for better UX
      await new Promise(resolve => setTimeout(resolve, 300));
      setCurrentView(newView);

      // Update URL hash
      window.location.hash = newView;

      console.log(`🔄 View changed to: ${newView}`);
    } catch (err) {
      setError('Failed to load view');
      console.error('View change error:', err);
    } finally {
      setIsTransitioning(false);
    }
  }, [currentView]);



  // Initialize dashboard and auto-refresh
  useEffect(() => {
    // Listen for hash changes to handle navigation
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash && navigationConfig[hash as keyof typeof navigationConfig]) {
        setCurrentView(hash);
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    // Initial data fetch
    fetchDashboardData();

    // Set up auto-refresh if enabled
    let autoRefreshCleanup: (() => void) | null = null;
    if (autoRefreshEnabled) {
      autoRefreshCleanup = comprehensiveAdminApiService.startAutoRefresh(120000); // 2 minutes to reduce server load

      // Listen for cache clears to refresh UI
      const refreshInterval = setInterval(() => {
        if (autoRefreshEnabled && !loading) {
          console.log('🔄 Auto-refresh triggered');
          fetchDashboardData();
        }
      }, 120000); // 2 minutes to reduce server load

      const originalCleanup = autoRefreshCleanup;
      autoRefreshCleanup = () => {
        clearInterval(refreshInterval);
        if (originalCleanup) originalCleanup();
      };
    }

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
      if (autoRefreshCleanup) autoRefreshCleanup();
      comprehensiveAdminApiService.cleanup();
    };
  }, [autoRefreshEnabled]); // Removed fetchDashboardData and loading from dependencies to prevent infinite loop

  // Authentication check on component mount
  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('🔐 No authentication token found');
          setIsAuthenticated(false);
          setAuthLoading(false);
          return;
        }

        // Verify token with backend
        const response = await fetch('http://localhost:4000/api/auth/verify', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Authentication verified:', data);
          setIsAuthenticated(true);
        } else {
          console.log('❌ Authentication failed');
          setIsAuthenticated(false);
          localStorage.removeItem('authToken');
        }
      } catch (error) {
        console.error('💥 Authentication check error:', error);
        setIsAuthenticated(false);
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  // Load profile data when profile modal is opened
  useEffect(() => {
    if (showProfileModal) {
      // Check if user is authenticated first
      const token = localStorage.getItem('authToken');
      if (!token) {
        alert('Please log in first to access Profile Management');
        setShowProfileModal(false);
        return;
      }
      loadProfile();
    }
  }, [showProfileModal]);

  // Render content based on current view
  const renderContent = () => {
    if (isTransitioning) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-8">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading...</h3>
            <p className="text-gray-600">Please wait while we load the content</p>
          </div>
        </div>
      );
    }

    switch (currentView) {
      // Phase 1 Views
      case 'tenant-management':
        return renderTenantManagement();
      case 'user-management':
        return renderUserManagement();
      case 'billing-management':
        return renderBillingManagement();
      case 'system-metrics':
        return renderSystemMetrics();


      // Phase 2 Views
      case 'advanced-analytics':
        return renderAdvancedAnalytics();
      case 'security-audit':
        return renderSecurityAudit();
      case 'backup-management':
        return renderBackupManagement();
      case 'api-management':
        return renderAPIManagement();
      case 'performance-monitor':
        return renderPerformanceMonitor();
      case 'system-optimization':
        return renderSystemOptimization();

      // Phase 3 Views
      case 'ai-analytics':
        return renderAIAnalytics();
      case 'predictive-forecasting':
        return renderPredictiveForecasting();
      case 'inventory-optimization':
        return renderInventoryOptimization();
      case 'smart-pricing':
        return renderSmartPricing();
      case 'staff-optimization':
        return renderStaffOptimization();
      case 'customer-behavior':
        return renderCustomerBehavior();

      case 'multi-currency':
        return renderMultiCurrency();

      case 'dashboard':
      default:
        return renderDashboardOverview();
    }
  };

  // Dashboard Overview (Main Dashboard) - Optimized for viewport
  const renderDashboardOverview = () => {
    return (
      <div className="h-full p-4 space-y-4">
        {/* Compact Header */}
        <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold mb-1">Super Admin Dashboard</h2>
              <p className="text-red-100 text-sm">Multi-tenant POS ecosystem with AI intelligence</p>
              <div className="flex items-center space-x-3 mt-2">
                <div className="flex items-center space-x-1">
                  <Database className="h-4 w-4" />
                  <span className="text-xs">PostgreSQL: {databaseConnected ? 'Connected' : 'Disconnected'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span className="text-xs">Updated: {lastRefresh.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="bg-white/20 hover:bg-white/30 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1"
              >
                <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                databaseConnected ? 'bg-green-500/20 text-green-100' : 'bg-red-500/20 text-red-100'
              }`}>
                {databaseConnected ? '🟢 LIVE' : '🔴 OFFLINE'}
              </div>
            </div>
          </div>
        </div>

        {/* Compact System Metrics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-gray-600">Total Tenants</p>
              <p className="text-xl font-bold text-gray-900">{metrics?.totalTenants || 0}</p>
              <p className="text-xs text-green-600">{metrics?.activeTenants || 0} active</p>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-gray-600">Monthly Revenue</p>
              <p className="text-xl font-bold text-gray-900">${(metrics?.monthlyRevenue || 0).toLocaleString()}</p>
              <p className="text-xs text-green-600">+{analytics?.growthRate || 0}% growth</p>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Activity className="h-4 w-4 text-orange-600" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-gray-600">System Uptime</p>
              <p className="text-xl font-bold text-gray-900">{(metrics?.systemUptime || 0).toFixed(1)}%</p>
              <p className="text-xs text-green-600">{metrics?.responseTime || 0}ms avg</p>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Brain className="h-4 w-4 text-purple-600" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-gray-600">AI Predictions</p>
              <p className="text-xl font-bold text-gray-900">${(aiAnalytics?.predictedRevenue || 0).toLocaleString()}</p>
              <p className="text-xs text-green-600">{aiAnalytics?.forecastAccuracy || 0}% accuracy</p>
            </div>
          </div>
        </div>

        {/* Compact Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Core Management */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-bold text-gray-900">Core Management</h3>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">Essential</span>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleViewChange('tenant-management')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200">
                    <Users className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Tenant Management</div>
                    <div className="text-xs text-gray-600">{tenants.length} tenants</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('user-management')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200">
                    <Shield className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">User Management</div>
                    <div className="text-xs text-gray-600">{users.length} users</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('billing-management')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center group-hover:bg-emerald-200">
                    <DollarSign className="h-4 w-4 text-emerald-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Billing & Plans</div>
                    <div className="text-xs text-gray-600">Revenue management</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Advanced Features */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-bold text-gray-900">Advanced Features</h3>
              <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">Pro</span>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleViewChange('advanced-analytics')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-cyan-300 hover:bg-cyan-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center group-hover:bg-cyan-200">
                    <BarChart3 className="h-4 w-4 text-cyan-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Advanced Analytics</div>
                    <div className="text-xs text-gray-600">Deep insights</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('security-audit')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-red-300 hover:bg-red-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200">
                    <Lock className="h-4 w-4 text-red-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Security Audit</div>
                    <div className="text-xs text-gray-600">{securityAudits.length} events</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('performance-monitor')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200">
                    <Zap className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Performance Monitor</div>
                    <div className="text-xs text-gray-600">System optimization</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* AI Intelligence */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-bold text-gray-900">AI Intelligence</h3>
              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">AI</span>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleViewChange('ai-analytics')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-violet-300 hover:bg-violet-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-violet-100 rounded-lg flex items-center justify-center group-hover:bg-violet-200">
                    <Brain className="h-4 w-4 text-violet-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">AI Analytics</div>
                    <div className="text-xs text-gray-600">Machine learning insights</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('predictive-forecasting')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-pink-300 hover:bg-pink-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center group-hover:bg-pink-200">
                    <TrendingUp className="h-4 w-4 text-pink-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Predictive Forecasting</div>
                    <div className="text-xs text-gray-600">Revenue predictions</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => handleViewChange('smart-pricing')}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-amber-300 hover:bg-amber-50 transition-all duration-200 group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center group-hover:bg-amber-200">
                    <Target className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Smart Pricing</div>
                    <div className="text-xs text-gray-600">AI-powered optimization</div>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Compact Activity Feed */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-bold text-gray-900">System Activity</h3>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-600">Live</span>
            </div>
          </div>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {loading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-gray-300 animate-pulse"></div>
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-300 rounded animate-pulse w-32"></div>
                      <div className="h-2 bg-gray-200 rounded animate-pulse w-16"></div>
                    </div>
                  </div>
                  <div className="h-2 bg-gray-200 rounded animate-pulse w-12"></div>
                </div>
              ))
            ) : activities.length > 0 ? (
              activities.slice(0, 4).map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'success' ? 'bg-green-500' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      activity.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></div>
                    <div>
                      <p className="text-xs font-medium text-gray-900">{activity.action}</p>
                      <p className="text-xs text-gray-500">{activity.tenant}</p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-400">{new Date(activity.timestamp).toLocaleTimeString()}</span>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                <Activity className="h-6 w-6 mx-auto mb-1 opacity-50" />
                <p className="text-xs">No recent activity</p>
              </div>
            )}
          </div>
          {!loading && activities.length > 4 && (
            <div className="mt-3 text-center">
              <button
                onClick={() => handleViewChange('system-activity')}
                className="text-xs text-blue-600 hover:text-blue-700 font-medium"
              >
                View all activity →
              </button>
            </div>
          )}
        </div>

        {/* Compact Database Status */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Database className="h-6 w-6 text-gray-600" />
              <div>
                <h3 className="text-base font-bold text-gray-900">PostgreSQL Database</h3>
                <p className="text-xs text-gray-600">BARPOS Database • localhost:5432</p>
              </div>
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              databaseConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {databaseConnected ? '🟢 Connected' : '🔴 Disconnected'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Optimized Render Functions
  const renderTenantManagement = () => (
    <div className="h-full p-4">
      <TenantManagement />
    </div>
  );

  const renderUserManagement = () => (
    <div className="h-full p-4">
      <EnhancedUserManager />
    </div>
  );

  const renderBillingManagement = () => (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Billing & Plans Management</h2>
          <p className="text-gray-600">Manage subscription plans, billing, and revenue tracking</p>
        </div>
        <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Create Plan</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Monthly Revenue</h3>
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">${(metrics?.monthlyRevenue || 0).toLocaleString()}</p>
          <p className="text-sm text-gray-600 mt-2">+{analytics?.growthRate || 0}% from last month</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Active Subscriptions</h3>
            <Users className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">{tenants.filter(t => t.status === 'active').length}</p>
          <p className="text-sm text-gray-600 mt-2">Across all plans</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Churn Rate</h3>
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">{analytics?.churnRate || 0}%</p>
          <p className="text-sm text-gray-600 mt-2">This month</p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Subscription Plans</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {['Basic', 'Pro', 'Enterprise'].map((plan) => (
            <div key={plan} className="border rounded-lg p-6">
              <h4 className="text-lg font-semibold mb-2">{plan}</h4>
              <p className="text-2xl font-bold mb-4">
                ${plan === 'Basic' ? '29' : plan === 'Pro' ? '79' : '199'}/month
              </p>
              <div className="space-y-2 mb-4">
                <p className="text-sm text-gray-600">✓ Core POS features</p>
                <p className="text-sm text-gray-600">✓ Basic analytics</p>
                {plan !== 'Basic' && <p className="text-sm text-gray-600">✓ Advanced features</p>}
                {plan === 'Enterprise' && <p className="text-sm text-gray-600">✓ AI-powered insights</p>}
              </div>
              <p className="text-sm text-gray-600">
                {tenants.filter(t => t.plan === plan.toLowerCase()).length} active subscriptions
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSystemMetrics = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">System Metrics</h2>
        <p className="text-gray-600">Real-time system performance and health monitoring</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">System Uptime</h3>
            <Server className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">{(metrics?.systemUptime || 0).toFixed(1)}%</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Response Time</h3>
            <Clock className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">{metrics?.responseTime || 0}ms</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Error Rate</h3>
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </div>
          <p className="text-2xl font-bold text-red-600">{(metrics?.errorRate || 0).toFixed(2)}%</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">API Requests</h3>
            <Activity className="h-5 w-5 text-purple-600" />
          </div>
          <p className="text-2xl font-bold text-purple-600">{(metrics?.apiRequests || 0).toLocaleString()}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Resource Usage</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Memory Usage</span>
                <span>{metrics?.memoryUsage || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${metrics?.memoryUsage || 0}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>CPU Usage</span>
                <span>{metrics?.cpuUsage || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${metrics?.cpuUsage || 0}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Disk Usage</span>
                <span>{metrics?.diskUsage || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-orange-600 h-2 rounded-full"
                  style={{ width: `${metrics?.diskUsage || 0}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Database Connections</h3>
          <div className="text-center">
            <p className="text-4xl font-bold text-blue-600 mb-2">{metrics?.databaseConnections || 0}</p>
            <p className="text-gray-600">Active connections</p>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                PostgreSQL Database: {databaseConnected ? 'Connected' : 'Disconnected'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Enhanced Profile Management Component with PIN Authentication
  const ProfileManagementComponent: React.FC = () => {
    const [pinAuthRequired, setPinAuthRequired] = useState(true);
    const [pinAuthInput, setPinAuthInput] = useState('');
    const [pinAuthError, setPinAuthError] = useState('');
    const [pinAuthLoading, setPinAuthLoading] = useState(false);
    const [activityLogs, setActivityLogs] = useState<any[]>([]);
    const [showActivityLogs, setShowActivityLogs] = useState(false);
    const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
    const [uploadingPicture, setUploadingPicture] = useState(false);

    // Debug logging for state changes
    useEffect(() => {
      console.log('🔍 ProfileManagementComponent state:', {
        pinAuthRequired,
        profileLoading,
        profile: profile ? 'loaded' : 'null',
        profileError
      });
    }, [pinAuthRequired, profileLoading, profile, profileError]);

    // PIN Authentication Handler
    const handlePinAuth = async () => {
      if (pinAuthInput.length !== 6) {
        setPinAuthError('PIN must be exactly 6 digits');
        return;
      }

      setPinAuthLoading(true);
      setPinAuthError('');

      try {
        // Use direct API call to verify PIN instead of the service method
        const token = localStorage.getItem('authToken');
        if (!token) {
          setPinAuthError('Authentication required. Please log in first.');
          setPinAuthLoading(false);
          return;
        }

        console.log('🔐 Verifying PIN for profile access...');
        const response = await fetch('http://localhost:4000/api/admin/profile/verify-pin', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ pin: pinAuthInput })
        });

        console.log('🔐 PIN verification response status:', response.status);

        if (response.ok) {
          const result = await response.json();
          console.log('✅ PIN verification successful:', result);

          // Clear PIN input and error
          setPinAuthInput('');
          setPinAuthError('');

          console.log('🔓 Immediately unlocking profile interface...');

          // Create a basic profile immediately to avoid loading issues
          const basicProfile = {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin',
            tenantName: 'BARPOS System',
            tenantSlug: 'barpos',
            lastLogin: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            isActive: true
          };

          // Set profile data immediately
          setProfile(basicProfile);
          setProfileFormData({
            name: basicProfile.name,
            email: basicProfile.email
          });

          // Unlock the interface immediately
          setPinAuthRequired(false);
          console.log('✅ Profile interface unlocked with basic profile');

          // Try to load real profile data in the background (non-blocking)
          setTimeout(async () => {
            try {
              console.log('🔄 Loading real profile data in background...');
              await loadProfileData();
              console.log('✅ Real profile data loaded successfully');
            } catch (profileError) {
              console.log('⚠️ Background profile loading failed, using basic profile');
            }
          }, 100);
        } else {
          const errorData = await response.json();
          console.log('❌ PIN verification failed:', errorData);
          setPinAuthError(errorData.error || errorData.message || 'Invalid PIN. Please try again.');
          setPinAuthInput('');
        }
      } catch (error) {
        console.error('💥 PIN verification error:', error);
        setPinAuthError('Authentication failed. Please try again.');
        setPinAuthInput('');
      } finally {
        setPinAuthLoading(false);
      }
    };

    // Load Profile Data (Simplified and robust)
    const loadProfileData = async () => {
      try {
        console.log('📊 Starting profile data load...');
        setProfileLoading(true);
        setProfileError(null);

        const token = localStorage.getItem('authToken');
        if (!token) {
          console.error('❌ No auth token found');
          throw new Error('Authentication required');
        }

        console.log('📡 Making API call to fetch profile...');

        // Add timeout to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const response = await fetch('http://localhost:4000/api/admin/profile', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        console.log('📡 Profile API response status:', response.status);

        if (response.ok) {
          const profileData = await response.json();
          console.log('✅ Profile data received:', profileData);

          setProfile(profileData);
          setProfileFormData({
            name: profileData.name || '',
            email: profileData.email || ''
          });

          console.log('✅ Profile state updated successfully');
        } else {
          throw new Error(`API returned ${response.status}`);
        }
      } catch (error) {
        console.log('⚠️ Profile API failed, using fallback data:', error.message);

        // Always provide a working profile
        const fallbackProfile = {
          id: 1,
          name: 'Super Admin',
          email: '<EMAIL>',
          role: 'super_admin',
          tenantName: 'BARPOS System',
          tenantSlug: 'barpos',
          lastLogin: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          isActive: true
        };

        setProfile(fallbackProfile);
        setProfileFormData({
          name: fallbackProfile.name,
          email: fallbackProfile.email
        });

        console.log('✅ Fallback profile data set');
      } finally {
        console.log('🏁 Profile loading complete');
        setProfileLoading(false);
      }
    };

    // Load Activity Logs
    const loadActivityLogs = async () => {
      try {
        const logs = await comprehensiveAdminApiService.getProfileActivityLogs();
        setActivityLogs(logs);
        setShowActivityLogs(true);
      } catch (error) {
        console.error('Failed to load activity logs:', error);
      }
    };

    // Enhanced Profile Update Handler
    const handleProfileUpdate = async (e: React.FormEvent) => {
      e.preventDefault();
      try {
        setProfileLoading(true);
        setProfileError(null);

        const updatedProfile = await comprehensiveAdminApiService.updateProfile(profileFormData);
        setProfile(updatedProfile);
        setIsEditingProfile(false);
        setSuccess('Profile updated successfully');
      } catch (error) {
        setProfileError(error instanceof Error ? error.message : 'Failed to update profile');
      } finally {
        setProfileLoading(false);
      }
    };

    // Enhanced PIN Change Handler
    const handlePinChange = async (e: React.FormEvent) => {
      e.preventDefault();

      if (pinFormData.newPin !== pinFormData.confirmPin) {
        setProfileError('New PIN and confirmation do not match');
        return;
      }

      if (pinFormData.newPin.length !== 6) {
        setProfileError('PIN must be exactly 6 digits');
        return;
      }

      try {
        setProfileLoading(true);
        setProfileError(null);

        await comprehensiveAdminApiService.changePin(pinFormData.currentPin, pinFormData.newPin);

        setPinFormData({
          currentPin: '',
          newPin: '',
          confirmPin: ''
        });
        setShowPinModal(false);
        setSuccess('PIN changed successfully');
      } catch (error) {
        setProfileError(error instanceof Error ? error.message : 'Failed to change PIN');
      } finally {
        setProfileLoading(false);
      }
    };

    // Profile Picture Upload Handler
    const handleProfilePictureUpload = async () => {
      if (!profilePictureFile) return;

      try {
        setUploadingPicture(true);
        await comprehensiveAdminApiService.uploadProfilePicture(profilePictureFile);
        setSuccess('Profile picture uploaded successfully');
        setProfilePictureFile(null);
        await loadProfileData(); // Reload profile to get new picture URL
      } catch (error) {
        setProfileError('Failed to upload profile picture');
      } finally {
        setUploadingPicture(false);
      }
    };

    // PIN Authentication Gate
    if (pinAuthRequired) {
      console.log('🔒 Rendering PIN authentication gate');
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center p-6">
          <div className="bg-white rounded-2xl shadow-2xl border border-red-100 p-8 w-full max-w-md">
            <div className="text-center mb-8">
              <div className="bg-red-100 rounded-full p-4 w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <Shield className="h-10 w-10 text-red-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Secure Access Required</h2>
              <p className="text-gray-600">Enter your PIN to access Profile Management</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter 6-Digit PIN
                </label>
                <input
                  type="password"
                  value={pinAuthInput}
                  onChange={(e) => setPinAuthInput(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-center text-2xl tracking-widest"
                  placeholder="••••••"
                  maxLength={6}
                />
              </div>

              {pinAuthError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{pinAuthError}</p>
                </div>
              )}

              <button
                onClick={handlePinAuth}
                disabled={pinAuthLoading || pinAuthInput.length !== 6}
                className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {pinAuthLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Verifying...</span>
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4" />
                    <span>Access Profile</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      );
    }

    if (profileLoading) {
      console.log('⏳ Rendering profile loading state');
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center p-6">
          <div className="bg-white rounded-2xl shadow-2xl border border-red-100 p-8 w-full max-w-md">
            <div className="text-center">
              <div className="bg-red-100 rounded-full p-4 w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <Settings className="h-10 w-10 text-red-600 animate-pulse" />
              </div>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">Loading Profile</h2>
              <p className="text-gray-600">Setting up your Super Admin profile...</p>
              <div className="mt-4 text-xs text-gray-500">
                This may take a few seconds while we fetch your data
              </div>
            </div>
          </div>
        </div>
      );
    }

    console.log('✅ Rendering main profile interface', { profile, profileError });

    return (
      <div className="space-y-4">
        {/* Compact Header */}
        <div className="flex items-center justify-between border-b border-gray-200 pb-4">
          <div>
            <h3 className="text-xl font-bold text-gray-900 flex items-center space-x-2">
              <div className="bg-red-100 rounded-full p-1">
                <Settings className="h-5 w-5 text-red-600" />
              </div>
              <span>Profile Management</span>
            </h3>
            <p className="text-gray-600 text-sm mt-1">Manage your Super Admin profile and security settings</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={loadActivityLogs}
              className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-1 transition-colors text-sm"
            >
              <Eye className="h-4 w-4" />
              <span>Activity</span>
            </button>
            <button
              onClick={() => setPinAuthRequired(true)}
              className="bg-gray-600 text-white px-3 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-1 transition-colors text-sm"
            >
              <Lock className="h-4 w-4" />
              <span>Lock</span>
            </button>
          </div>
        </div>

        {/* Enhanced Alerts */}
        {profileError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{profileError}</span>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-green-700">{success}</span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Profile Information */}
          <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Settings className="h-5 w-5 text-red-600" />
              <h4 className="text-lg font-semibold text-gray-900">Profile Information</h4>
            </div>

            {/* Simplified Profile Picture */}
            <div className="flex items-center space-x-3 mb-4 p-3 bg-white rounded-lg border">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <Settings className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{profile?.name || 'Super Admin'}</p>
                <p className="text-sm text-gray-600">{profile?.role || 'super_admin'}</p>
              </div>
            </div>

            <form onSubmit={handleProfileUpdate} className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  type="text"
                  value={profileFormData.name}
                  onChange={(e) => setProfileFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <input
                  type="email"
                  value={profileFormData.email}
                  onChange={(e) => setProfileFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <input
                    type="text"
                    value={profile?.role || 'super_admin'}
                    disabled
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tenant</label>
                  <input
                    type="text"
                    value={profile?.tenantName || 'BARPOS System'}
                    disabled
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 text-sm"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={profileLoading}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
              >
                <Save className="h-4 w-4" />
                <span>{profileLoading ? 'Saving...' : 'Update Profile'}</span>
              </button>
            </form>
          </div>

          {/* Security Settings */}
          <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Lock className="h-5 w-5 text-red-600" />
              <h4 className="text-lg font-semibold text-gray-900">Security Settings</h4>
            </div>

            <form onSubmit={handlePinChange} className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current PIN</label>
                <input
                  type="password"
                  value={pinFormData.currentPin}
                  onChange={(e) => setPinFormData(prev => ({ ...prev, currentPin: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  placeholder="Enter current PIN"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">New PIN</label>
                <input
                  type="password"
                  value={pinFormData.newPin}
                  onChange={(e) => setPinFormData(prev => ({ ...prev, newPin: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  placeholder="Enter new PIN (6 digits)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Confirm New PIN</label>
                <input
                  type="password"
                  value={pinFormData.confirmPin}
                  onChange={(e) => setPinFormData(prev => ({ ...prev, confirmPin: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  placeholder="Confirm new PIN"
                />
              </div>

              <button
                type="submit"
                disabled={profileLoading}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
              >
                <Key className="h-4 w-4" />
                <span>{profileLoading ? 'Changing...' : 'Change PIN'}</span>
              </button>
            </form>

            {/* Security Status */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h5 className="text-sm font-medium text-gray-700 mb-2">Security Status</h5>
              <div className="space-y-1 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Two-Factor Authentication</span>
                  <span className="text-red-600 font-medium">Disabled</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Session Timeout</span>
                  <span className="text-green-600 font-medium">30 minutes</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Last Login</span>
                  <span className="text-gray-600">{profile?.lastLogin ? new Date(profile.lastLogin).toLocaleDateString() : 'Today'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderProfileManagement = () => <ProfileManagementComponent />;

  // Phase 2 Render Functions
  const renderAdvancedAnalytics = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics</h2>
        <p className="text-gray-600">Deep insights into system performance and user behavior</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Daily Active Users</h3>
            <Users className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">{analytics?.dailyActiveUsers || 0}</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Total Transactions</h3>
            <DollarSign className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">{(analytics?.totalTransactions || 0).toLocaleString()}</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Conversion Rate</h3>
            <TrendingUp className="h-5 w-5 text-purple-600" />
          </div>
          <p className="text-2xl font-bold text-purple-600">{analytics?.conversionRate || 0}%</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Avg Order Value</h3>
            <BarChart3 className="h-5 w-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-orange-600">${analytics?.averageOrderValue || 0}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Popular Features</h3>
          <div className="space-y-3">
            {analytics?.popularFeatures?.map((feature, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium">{feature.name}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${feature.usage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600">{feature.usage}%</span>
                </div>
              </div>
            )) || <p className="text-gray-500">No data available</p>}
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Average Response Time</span>
              <span className="text-sm font-medium">{analytics?.performanceMetrics?.avgResponseTime || 0}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">System Uptime</span>
              <span className="text-sm font-medium">{analytics?.performanceMetrics?.uptime || 0}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Error Rate</span>
              <span className="text-sm font-medium">{analytics?.performanceMetrics?.errorRate || 0}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Throughput</span>
              <span className="text-sm font-medium">{analytics?.performanceMetrics?.throughput || 0} req/s</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityAudit = () => (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Security Audit</h2>
          <p className="text-gray-600">Monitor security events and system access logs</p>
        </div>
        <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2">
          <Download className="h-4 w-4" />
          <span>Export Report</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Total Events</h3>
            <Shield className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">{securityAudits.length}</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Critical Events</h3>
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </div>
          <p className="text-2xl font-bold text-red-600">
            {securityAudits.filter(audit => audit.severity === 'critical').length}
          </p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Open Issues</h3>
            <AlertCircle className="h-5 w-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-orange-600">
            {securityAudits.filter(audit => audit.status === 'open').length}
          </p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Resolved</h3>
            <CheckCircle className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">
            {securityAudits.filter(audit => audit.status === 'resolved').length}
          </p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Recent Security Events</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {securityAudits.slice(0, 10).map((audit) => (
                <tr key={audit.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {audit.timestamp ? new Date(audit.timestamp).toLocaleString() : 'Unknown'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {audit.event}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      audit.severity === 'critical' ? 'bg-red-100 text-red-800' :
                      audit.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                      audit.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {audit.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {audit.userEmail}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {audit.ipAddress}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      audit.status === 'resolved' ? 'bg-green-100 text-green-800' :
                      audit.status === 'investigating' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {audit.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderBackupManagement = () => (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Backup & Recovery Management</h2>
          <p className="text-gray-600">Manage database backups and system recovery options</p>
        </div>
        <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-2">
          <Upload className="h-4 w-4" />
          <span>Create Backup</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Last Backup</h3>
            <Database className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-sm text-gray-600">2 hours ago</p>
          <p className="text-xs text-green-600 mt-1">✓ Successful</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Backup Size</h3>
            <Server className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-2xl font-bold">2.4 GB</p>
          <p className="text-xs text-gray-600 mt-1">Compressed</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Retention</h3>
            <Clock className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-2xl font-bold">30 days</p>
          <p className="text-xs text-gray-600 mt-1">Auto cleanup</p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Backup Schedule</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Daily Full Backup</h4>
              <p className="text-sm text-gray-600">Every day at 2:00 AM</p>
            </div>
            <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Active</span>
          </div>
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Hourly Incremental</h4>
              <p className="text-sm text-gray-600">Every hour during business hours</p>
            </div>
            <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Active</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAPIManagement = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">API Management</h2>
        <p className="text-gray-600">Monitor API usage, manage keys, and configure rate limits</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Total Requests</h3>
            <Activity className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">{(metrics?.apiRequests || 0).toLocaleString()}</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Success Rate</h3>
            <CheckCircle className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">99.8%</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Avg Response</h3>
            <Clock className="h-5 w-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-orange-600">{metrics?.responseTime || 0}ms</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Active Keys</h3>
            <Lock className="h-5 w-5 text-purple-600" />
          </div>
          <p className="text-2xl font-bold text-purple-600">24</p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">API Endpoints</h3>
        <div className="space-y-3">
          {[
            { endpoint: '/api/admin/tenants', method: 'GET', requests: 1250, status: 'healthy' },
            { endpoint: '/api/admin/users', method: 'GET', requests: 890, status: 'healthy' },
            { endpoint: '/api/admin/metrics', method: 'GET', requests: 2100, status: 'healthy' },
            { endpoint: '/api/admin/analytics', method: 'GET', requests: 650, status: 'warning' }
          ].map((api, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <span className={`px-2 py-1 text-xs font-medium rounded ${
                  api.method === 'GET' ? 'bg-blue-100 text-blue-800' :
                  api.method === 'POST' ? 'bg-green-100 text-green-800' :
                  'bg-orange-100 text-orange-800'
                }`}>
                  {api.method}
                </span>
                <span className="font-mono text-sm">{api.endpoint}</span>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">{api.requests} requests</span>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  api.status === 'healthy' ? 'bg-green-100 text-green-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {api.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPerformanceMonitor = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Performance Monitor</h2>
        <p className="text-gray-600">Real-time system performance monitoring and optimization</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">CPU Usage</h3>
            <Zap className="h-5 w-5 text-yellow-600" />
          </div>
          <p className="text-2xl font-bold text-yellow-600">{metrics?.cpuUsage || 0}%</p>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div
              className="bg-yellow-600 h-2 rounded-full"
              style={{ width: `${metrics?.cpuUsage || 0}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Memory Usage</h3>
            <Server className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">{metrics?.memoryUsage || 0}%</p>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${metrics?.memoryUsage || 0}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Disk Usage</h3>
            <Database className="h-5 w-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-orange-600">{metrics?.diskUsage || 0}%</p>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div
              className="bg-orange-600 h-2 rounded-full"
              style={{ width: `${metrics?.diskUsage || 0}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Network I/O</h3>
            <Globe className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">2.4 GB/s</p>
          <p className="text-xs text-gray-600 mt-1">Throughput</p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Performance Recommendations</h3>
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Database Optimization</h4>
              <p className="text-sm text-blue-700">Consider adding indexes to improve query performance</p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900">Memory Usage</h4>
              <p className="text-sm text-green-700">Memory usage is within optimal range</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSystemOptimization = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">System Optimization</h2>
        <p className="text-gray-600">Advanced optimization recommendations and performance analysis</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Optimization Score</h3>
            <TrendingUp className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">87%</p>
          <p className="text-sm text-gray-600 mt-2">System performance rating</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Potential Savings</h3>
            <DollarSign className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">$2,400</p>
          <p className="text-sm text-gray-600 mt-2">Monthly cost reduction</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Efficiency Gain</h3>
            <Zap className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">23%</p>
          <p className="text-sm text-gray-600 mt-2">Performance improvement</p>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Optimization Recommendations</h3>
        <div className="space-y-4">
          {[
            { title: 'Database Query Optimization', impact: 'High', effort: 'Medium', savings: '$800/month' },
            { title: 'Cache Implementation', impact: 'High', effort: 'Low', savings: '$600/month' },
            { title: 'Resource Scaling', impact: 'Medium', effort: 'High', savings: '$1000/month' }
          ].map((rec, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{rec.title}</h4>
                <div className="flex items-center space-x-4 mt-1">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    rec.impact === 'High' ? 'bg-red-100 text-red-800' :
                    rec.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {rec.impact} Impact
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    rec.effort === 'High' ? 'bg-red-100 text-red-800' :
                    rec.effort === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {rec.effort} Effort
                  </span>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-green-600">{rec.savings}</p>
                <button className="text-sm text-blue-600 hover:text-blue-800">Apply →</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Phase 3 Render Functions - AI-Powered Intelligence
  const renderAIAnalytics = () => (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Analytics Dashboard</h2>
          <p className="text-gray-600">Machine learning insights and intelligent recommendations</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
            🤖 AI Powered
          </span>
          <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
            {aiAnalytics?.forecastAccuracy || 0}% Accuracy
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Predicted Revenue</h3>
            <Brain className="h-5 w-5 text-purple-600" />
          </div>
          <p className="text-2xl font-bold text-purple-600">
            ${(aiAnalytics?.predictedRevenue || 0).toLocaleString()}
          </p>
          <p className="text-xs text-green-600 mt-1">+15% vs last month</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Customer Segments</h3>
            <Users className="h-5 w-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-blue-600">
            {aiAnalytics?.customerBehavior?.segmentCount || 0}
          </p>
          <p className="text-xs text-gray-600 mt-1">Active segments</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Optimization Savings</h3>
            <Target className="h-5 w-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-green-600">
            ${(aiAnalytics?.inventoryOptimization?.optimizationSavings || 0).toLocaleString()}
          </p>
          <p className="text-xs text-green-600 mt-1">This month</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">Pricing Changes</h3>
            <DollarSign className="h-5 w-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-orange-600">
            {aiAnalytics?.pricingOptimization?.recommendedChanges || 0}
          </p>
          <p className="text-xs text-orange-600 mt-1">Recommendations</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Inventory Optimization</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Overstocked Items</span>
              <span className="font-semibold text-red-600">
                {aiAnalytics?.inventoryOptimization?.overstockedItems || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Understocked Items</span>
              <span className="font-semibold text-orange-600">
                {aiAnalytics?.inventoryOptimization?.understockedItems || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Potential Savings</span>
              <span className="font-semibold text-green-600">
                ${(aiAnalytics?.inventoryOptimization?.optimizationSavings || 0).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Customer Behavior Insights</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Churn Prediction</span>
              <span className="font-semibold text-red-600">
                {aiAnalytics?.customerBehavior?.churnPrediction || 0}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Avg Lifetime Value</span>
              <span className="font-semibold text-green-600">
                ${(aiAnalytics?.customerBehavior?.lifetimeValue || 0).toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Segments</span>
              <span className="font-semibold text-blue-600">
                {aiAnalytics?.customerBehavior?.segmentCount || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">AI Recommendations</h3>
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-4 bg-purple-50 rounded-lg">
            <Brain className="h-5 w-5 text-purple-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-purple-900">Smart Pricing Optimization</h4>
              <p className="text-sm text-purple-700">
                AI suggests increasing prices for high-demand items by 8% to maximize revenue
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
            <Target className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Inventory Rebalancing</h4>
              <p className="text-sm text-blue-700">
                Reduce stock for 12 slow-moving items and increase for 8 high-demand products
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
            <Users className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900">Customer Retention</h4>
              <p className="text-sm text-green-700">
                Target at-risk customers with personalized offers to reduce churn by 25%
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPredictiveForecasting = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Predictive Sales Forecasting</h2>
        <p className="text-gray-600">AI-powered revenue predictions and demand forecasting</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Next Month Forecast</h3>
            <TrendingUp className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            ${(aiAnalytics?.predictedRevenue || 0).toLocaleString()}
          </p>
          <p className="text-sm text-gray-600 mt-2">
            {aiAnalytics?.forecastAccuracy || 0}% confidence
          </p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Growth Prediction</h3>
            <BarChart3 className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">+18%</p>
          <p className="text-sm text-gray-600 mt-2">Compared to last month</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Seasonal Impact</h3>
            <Calendar className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">+12%</p>
          <p className="text-sm text-gray-600 mt-2">Holiday season boost</p>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Revenue Forecast Chart</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">Interactive forecast chart would be displayed here</p>
            <p className="text-sm text-gray-500">Showing 12-month revenue predictions with confidence intervals</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Forecast Factors</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-3">Positive Factors</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Seasonal demand increase</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">New customer acquisition</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Product line expansion</span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-3">Risk Factors</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm">Market competition</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm">Economic uncertainty</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm">Supply chain issues</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderInventoryOptimization = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Intelligent Inventory Optimization</h2>
        <p className="text-gray-600">AI-powered inventory management and optimization recommendations</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Overstocked Items</h3>
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <p className="text-3xl font-bold text-red-600">
            {aiAnalytics?.inventoryOptimization?.overstockedItems || 0}
          </p>
          <p className="text-sm text-gray-600 mt-2">Items to reduce</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Understocked Items</h3>
            <AlertCircle className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">
            {aiAnalytics?.inventoryOptimization?.understockedItems || 0}
          </p>
          <p className="text-sm text-gray-600 mt-2">Items to increase</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Potential Savings</h3>
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            ${(aiAnalytics?.inventoryOptimization?.optimizationSavings || 0).toLocaleString()}
          </p>
          <p className="text-sm text-gray-600 mt-2">Monthly savings</p>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">AI Optimization Recommendations</h3>
        <div className="space-y-4">
          {[
            { item: 'Premium Coffee Beans', action: 'Reduce', current: 150, recommended: 100, impact: 'High' },
            { item: 'Seasonal Pastries', action: 'Increase', current: 50, recommended: 80, impact: 'Medium' },
            { item: 'Specialty Drinks Mix', action: 'Reduce', current: 200, recommended: 120, impact: 'High' }
          ].map((rec, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{rec.item}</h4>
                <p className="text-sm text-gray-600">
                  Current: {rec.current} units → Recommended: {rec.recommended} units
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  rec.action === 'Reduce' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                }`}>
                  {rec.action}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  rec.impact === 'High' ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800'
                }`}>
                  {rec.impact} Impact
                </span>
                <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  Apply
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSmartPricing = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Smart Pricing Optimization</h2>
        <p className="text-gray-600">AI-powered dynamic pricing and revenue optimization</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Price Changes</h3>
            <Target className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">
            {aiAnalytics?.pricingOptimization?.recommendedChanges || 0}
          </p>
          <p className="text-sm text-gray-600 mt-2">Recommendations</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Revenue Increase</h3>
            <TrendingUp className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            +{aiAnalytics?.pricingOptimization?.potentialIncrease || 0}%
          </p>
          <p className="text-sm text-gray-600 mt-2">Potential uplift</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Market Position</h3>
            <BarChart3 className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-lg font-bold text-orange-600">Competitive</p>
          <p className="text-sm text-gray-600 mt-2">
            {aiAnalytics?.pricingOptimization?.competitiveAnalysis || 'Analysis pending'}
          </p>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Dynamic Pricing Recommendations</h3>
        <div className="space-y-4">
          {[
            { product: 'Signature Latte', current: '$4.50', recommended: '$4.85', change: '+7.8%', demand: 'High' },
            { product: 'Breakfast Sandwich', current: '$6.99', recommended: '$6.49', change: '-7.2%', demand: 'Low' },
            { product: 'Premium Blend', current: '$3.25', recommended: '$3.50', change: '+7.7%', demand: 'High' }
          ].map((item, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{item.product}</h4>
                <p className="text-sm text-gray-600">
                  Current: {item.current} → Recommended: {item.recommended}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  item.change.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {item.change}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  item.demand === 'High' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {item.demand} Demand
                </span>
                <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  Apply
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStaffOptimization = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Automated Staff Scheduling</h2>
        <p className="text-gray-600">AI-powered workforce management and predictive scheduling</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Optimal Scheduling</h3>
            <Calendar className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">
            {aiAnalytics?.staffOptimization?.optimalScheduling || 0}%
          </p>
          <p className="text-sm text-gray-600 mt-2">Efficiency rating</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Labor Cost Savings</h3>
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            {aiAnalytics?.staffOptimization?.laborCostSavings || 0}%
          </p>
          <p className="text-sm text-gray-600 mt-2">Monthly reduction</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Productivity Increase</h3>
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">
            +{aiAnalytics?.staffOptimization?.productivityIncrease || 0}%
          </p>
          <p className="text-sm text-gray-600 mt-2">Performance boost</p>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">AI Scheduling Recommendations</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900">Peak Hours Optimization</h4>
            <p className="text-sm text-blue-700 mt-1">
              Add 2 additional staff members during lunch rush (11:30 AM - 1:30 PM) to reduce wait times
            </p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-900">Cost Reduction Opportunity</h4>
            <p className="text-sm text-green-700 mt-1">
              Reduce evening staff by 1 person on weekdays when customer traffic is 30% lower
            </p>
          </div>
          <div className="p-4 bg-orange-50 rounded-lg">
            <h4 className="font-medium text-orange-900">Skill-based Scheduling</h4>
            <p className="text-sm text-orange-700 mt-1">
              Schedule experienced baristas during morning rush for 15% faster service times
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCustomerBehavior = () => (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Customer Behavior AI Analysis</h2>
        <p className="text-gray-600">Advanced customer segmentation and behavioral insights</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Customer Segments</h3>
            <Users className="h-6 w-6 text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-blue-600">
            {aiAnalytics?.customerBehavior?.segmentCount || 0}
          </p>
          <p className="text-sm text-gray-600 mt-2">Active segments</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Churn Risk</h3>
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <p className="text-3xl font-bold text-red-600">
            {aiAnalytics?.customerBehavior?.churnPrediction || 0}%
          </p>
          <p className="text-sm text-gray-600 mt-2">At-risk customers</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Lifetime Value</h3>
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            ${(aiAnalytics?.customerBehavior?.lifetimeValue || 0).toLocaleString()}
          </p>
          <p className="text-sm text-gray-600 mt-2">Average CLV</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Customer Segments</h3>
          <div className="space-y-3">
            {[
              { name: 'Premium Customers', size: '25%', value: 'High', color: 'bg-green-500' },
              { name: 'Regular Visitors', size: '45%', value: 'Medium', color: 'bg-blue-500' },
              { name: 'Occasional Buyers', size: '20%', value: 'Low', color: 'bg-yellow-500' },
              { name: 'At-Risk Customers', size: '10%', value: 'Critical', color: 'bg-red-500' }
            ].map((segment, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${segment.color}`}></div>
                  <span className="font-medium">{segment.name}</span>
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium">{segment.size}</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    segment.value === 'High' ? 'bg-green-100 text-green-800' :
                    segment.value === 'Medium' ? 'bg-blue-100 text-blue-800' :
                    segment.value === 'Low' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {segment.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Behavioral Insights</h3>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900">Peak Visit Times</h4>
              <p className="text-sm text-blue-700">Most customers visit between 7-9 AM and 2-4 PM</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900">Popular Combinations</h4>
              <p className="text-sm text-green-700">Coffee + pastry combo has 78% purchase rate</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-900">Seasonal Preferences</h4>
              <p className="text-sm text-orange-700">Hot drinks increase 45% in winter months</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Phase 3G: Advanced Kitchen Display System - Moved to POS System
  // Kitchen Display is now available in the POS system under the "Kitchen Display" tab

  // Phase 3H: Multi-Currency Support System
  const renderMultiCurrency = () => (
    <div className="h-full">
      <Phase3HMultiCurrencySupport />
    </div>
  );

  // Show loading screen while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Show login interface if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Super Admin Access Required</h2>
            <p className="text-gray-600">Please log in with your Super Admin credentials to access the dashboard.</p>
          </div>
          <div className="text-center">
            <button
              onClick={() => window.location.href = '/login'}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
            >
              Go to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Main component return
  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Compact Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-red-600 to-pink-600 rounded-lg flex items-center justify-center">
                <Database className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">Super Admin Dashboard</h1>
                <p className="text-xs text-gray-500">Multi-Tenant POS Management</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="px-2 py-1 text-xs font-semibold bg-gradient-to-r from-red-100 to-pink-100 text-red-800 rounded-full">
                SUPER ADMIN
              </span>
              {databaseConnected ? (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </span>
              ) : (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-1"></div>
                  OFFLINE
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-right hidden md:block">
              <div className="text-sm font-medium text-gray-900">Super Administrator</div>
              <div className="text-xs text-gray-500">Last refresh: {lastRefresh.toLocaleTimeString()}</div>
            </div>

            <button
              onClick={() => setShowProfileModal(true)}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
              title="Manage Profile"
            >
              <UserIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Profile</span>
            </button>

            <div className="flex items-center space-x-1">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 disabled:opacity-50"
                title="Refresh Data"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={() => window.location.reload()}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Streamlined Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6">
          <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
            {Object.entries(navigationConfig).map(([key, config]) => {
              const isActive = currentView === key;

              return (
                <button
                  key={key}
                  onClick={() => handleViewChange(key)}
                  disabled={isTransitioning}
                  className={`flex items-center space-x-2 py-3 px-3 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                    isActive
                      ? `border-${config.color}-500 text-${config.color}-600 bg-${config.color}-50`
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  title={config.label}
                >
                  <span className="text-base">{config.icon}</span>
                  <span className="hidden sm:inline">{config.label}</span>
                  {isTransitioning && currentView === key && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-red-600 rounded-full animate-spin ml-1"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content Area - Fixed Height Layout */}
      <main className="flex-1 overflow-hidden bg-gray-50">
        <div className="h-full bg-white shadow-sm border-l border-r border-gray-200">
          {error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-6">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Database Connection Error</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <div className="space-x-2">
                  <button
                    onClick={() => {
                      setError(null);
                      fetchDashboardData(true);
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                  >
                    Retry Connection
                  </button>
                  <button
                    onClick={() => {
                      setError(null);
                      handleViewChange('dashboard');
                    }}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200"
                  >
                    Go to Dashboard
                  </button>
                </div>
                <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm text-gray-600">
                  <p><strong>Database:</strong> BARPOS • <strong>Host:</strong> localhost:5432 • <strong>Status:</strong> {databaseConnected ? 'Connected' : 'Disconnected'}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full overflow-auto">
              {renderContent()}
            </div>
          )}
        </div>
      </main>

      {/* Compact Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-6 py-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-xs font-medium text-gray-700">
                Tenants: <span className="font-bold text-blue-600">{metrics?.totalTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-xs font-medium text-gray-700">
                Active: <span className="font-bold text-green-600">{metrics?.activeTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${databaseConnected ? 'bg-emerald-400 animate-pulse' : 'bg-red-400'}`}></div>
              <span className="text-xs font-medium text-gray-700">
                DB: <span className={`font-bold ${databaseConnected ? 'text-emerald-600' : 'text-red-600'}`}>
                  {databaseConnected ? 'Connected' : 'Offline'}
                </span>
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span className="text-xs font-medium text-gray-700">
                View: <span className="font-bold text-purple-600 capitalize">{currentView.replace('-', ' ')}</span>
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-xs text-gray-500">
              {new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </div>
            <div className="text-xs font-mono text-gray-700">
              {new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' })}
            </div>
            <div className="px-2 py-1 bg-gradient-to-r from-red-100 to-pink-100 rounded text-xs font-medium text-red-700">
              v3.0.0
            </div>
          </div>
        </div>
      </footer>

      {/* Tenant Management Modal */}
      {showTenantModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                {tenantModalMode === 'create' ? 'Create New Tenant' :
                 tenantModalMode === 'edit' ? 'Edit Tenant' : 'Tenant Details'}
              </h3>
              <button
                onClick={() => setShowTenantModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tenant Name
                </label>
                <input
                  type="text"
                  value={tenantFormData.name}
                  onChange={(e) => setTenantFormData(prev => ({ ...prev, name: e.target.value }))}
                  disabled={tenantModalMode === 'view'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Enter tenant name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Slug
                </label>
                <input
                  type="text"
                  value={tenantFormData.slug}
                  onChange={(e) => setTenantFormData(prev => ({ ...prev, slug: e.target.value }))}
                  disabled={tenantModalMode === 'view'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Enter tenant slug"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={tenantFormData.email}
                  onChange={(e) => setTenantFormData(prev => ({ ...prev, email: e.target.value }))}
                  disabled={tenantModalMode === 'view'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={tenantFormData.phone}
                  onChange={(e) => setTenantFormData(prev => ({ ...prev, phone: e.target.value }))}
                  disabled={tenantModalMode === 'view'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <textarea
                  value={tenantFormData.address}
                  onChange={(e) => setTenantFormData(prev => ({ ...prev, address: e.target.value }))}
                  disabled={tenantModalMode === 'view'}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Enter address"
                />
              </div>

              {tenantModalMode !== 'create' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={tenantFormData.status}
                    onChange={(e) => setTenantFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' | 'suspended' }))}
                    disabled={tenantModalMode === 'view'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                  </select>
                </div>
              )}
            </div>

            {tenantModalMode !== 'view' && (
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowTenantModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveTenant}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Saving...' : (tenantModalMode === 'create' ? 'Create' : 'Update')}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && tenantToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-red-600">Delete Tenant</h3>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-2">
                Are you sure you want to delete the tenant <strong>{tenantToDelete.name}</strong>?
              </p>
              <p className="text-sm text-red-600">
                This action cannot be undone and will permanently remove all tenant data.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteTenant}
                disabled={loading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {loading ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Profile Management Modal */}
      {showProfileModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                <div className="bg-red-100 rounded-full p-2">
                  <UserIcon className="h-6 w-6 text-red-600" />
                </div>
                <span>Super Admin Profile Management</span>
              </h2>
              <button
                onClick={() => setShowProfileModal(false)}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
                title="Close Profile Management"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6">
              <ProfileManagementComponent />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}