import React, { useState, useEffect } from 'react';
import { EnhancedAppProvider, useEnhancedAppContext } from './context/EnhancedAppContext';
import EnhancedLogin from './components/EnhancedLogin';
import UnifiedProductGrid from './components/UnifiedProductGrid';
import UnifiedOrderPanel from './components/UnifiedOrderPanel';
import UnifiedFloorLayout from './components/UnifiedFloorLayout';
import EnhancedFloorLayout from './components/EnhancedFloorLayout';
import TableDetailsModal from './components/TableDetailsModal';
import ReservationManager from './components/ReservationManager';
import FloorLayoutPOSIntegration from './components/FloorLayoutPOSIntegration';
import UnifiedDineInWorkflowManager from './components/UnifiedDineInWorkflowManager';
import DineInWorkflowTester from './components/DineInWorkflowTester';
import Phase3AdvancedAnalytics from './components/Phase3AdvancedAnalytics';
import Phase3CRMSystem from './components/Phase3CRMSystem';
import Phase3InventoryManagement from './components/Phase3InventoryManagement';
import Phase3ReportingSystem from './components/Phase3ReportingSystem';
import UnifiedInventory from './components/UnifiedInventory';
import UnifiedStaffScheduling from './components/UnifiedStaffScheduling';
import MVPAnalytics from './components/MVPAnalytics';
import UserRegistration from './components/UserRegistration';
import AdvancedKitchenDisplay from './components/EnhancedKitchenDisplay';
import OrderQueue from './components/OrderQueue';
import BarTabManagement from './components/BarTabManagement';
import MultiLocationDashboard from './components/MultiLocationDashboard';
import CentralizedInventory from './components/CentralizedInventory';
import AdvancedAnalyticsDashboard from './components/AdvancedAnalyticsDashboard';
import PaymentTerminalManager from './components/PaymentTerminalManager';
import DigitalWalletPayments from './components/DigitalWalletPayments';
import POSHardwareManager from './components/POSHardwareManager';
import ReceiptPrinterManager from './components/ReceiptPrinterManager';
import BarcodeScannerManager from './components/BarcodeScannerManager';
import ComingSoonPlaceholder from './components/ComingSoonPlaceholder';
import TablePerformanceAnalytics from './components/TablePerformanceAnalytics';
import ServerPerformanceTracker from './components/ServerPerformanceTracker';
import OperationalInsightsDashboard from './components/OperationalInsightsDashboard';
import MultiLocationManager from './components/MultiLocationManager';
import CrossLocationAnalytics from './components/CrossLocationAnalytics';
import EnhancedPaymentProcessor from './components/EnhancedPaymentProcessor';
import OrderCompletionScreen from './components/OrderCompletionScreen';
import PaymentAnalyticsDashboard from './components/PaymentAnalyticsDashboard';
import EnhancedTenantAdminLandingPage from './components/EnhancedTenantAdminLandingPage';
import EnhancedFloorLayoutManager from './components/EnhancedFloorLayoutManager';
import EnhancedOrderPanelWithFloor from './components/EnhancedOrderPanelWithFloor';
import Phase4EnhancedPaymentProcessor from './components/Phase4EnhancedPaymentProcessor';
import Phase4HardwareManager from './components/Phase4HardwareManager';

// Role-based feature access configuration
const ROLE_PERMISSIONS = {
  super_admin: {
    tabs: ['pos', 'floor', 'inventory', 'staff', 'loyalty', 'analytics', 'reports', 'settings', 'kitchen', 'orders', 'tabs', 'locations', 'central-inventory', 'advanced-analytics', 'table-performance', 'server-performance', 'operational-insights', 'multi-location', 'cross-location-analytics', 'payment-analytics', 'payment-history', 'tenant-admin', 'payment-terminals', 'digital-wallets', 'hardware', 'printers', 'scanners', 'menu', 'qr', 'phase4-payments', 'phase4-hardware', 'workflow-tester'],
    features: ['all']
  },
  tenant_admin: {
    // Grant tenant_admin the same comprehensive access as super_admin for restaurant operations
    tabs: ['pos', 'floor', 'inventory', 'staff', 'loyalty', 'analytics', 'reports', 'settings', 'kitchen', 'orders', 'tabs', 'locations', 'central-inventory', 'advanced-analytics', 'table-performance', 'server-performance', 'operational-insights', 'multi-location', 'cross-location-analytics', 'payment-analytics', 'payment-history', 'tenant-admin', 'payment-terminals', 'digital-wallets', 'hardware', 'printers', 'scanners', 'menu', 'qr', 'phase4-payments', 'phase4-hardware', 'workflow-tester'],
    features: ['all'] // Grant all features for comprehensive restaurant management
  },
  admin: {
    // Alias for tenant_admin to handle legacy 'admin' role - same full privileges
    tabs: ['pos', 'floor', 'inventory', 'staff', 'loyalty', 'analytics', 'reports', 'settings', 'kitchen', 'orders', 'tabs', 'locations', 'central-inventory', 'advanced-analytics', 'table-performance', 'server-performance', 'operational-insights', 'multi-location', 'cross-location-analytics', 'payment-analytics', 'payment-history', 'tenant-admin', 'payment-terminals', 'digital-wallets', 'hardware', 'printers', 'scanners', 'menu', 'qr', 'phase4-payments', 'phase4-hardware', 'workflow-tester'],
    features: ['all'] // Grant all features for comprehensive restaurant management
  },
  manager: {
    tabs: ['pos', 'floor', 'inventory', 'staff', 'analytics', 'reports', 'settings', 'orders', 'tabs', 'table-performance', 'server-performance', 'operational-insights', 'payment-analytics', 'payment-history', 'payment-terminals', 'digital-wallets', 'phase4-payments', 'phase4-hardware'],
    features: ['inventory_management', 'staff_scheduling', 'basic_analytics', 'table_analytics', 'payment_processing', 'hardware_management']
  },
  employee: {
    tabs: ['pos', 'floor', 'orders'],
    features: ['basic_pos', 'floor_layout']
  },
  cashier: {
    tabs: ['pos'],
    features: ['basic_pos']
  }
};

// Main POS Interface Component
const UnifiedPOSInterface: React.FC = () => {
  const { state } = useEnhancedAppContext();
  const [activeTab, setActiveTab] = useState('pos');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDineInWorkflow, setShowDineInWorkflow] = useState(false);

  // Get user permissions based on role
  const userPermissions = ROLE_PERMISSIONS[state.currentEmployee?.role as keyof typeof ROLE_PERMISSIONS] || ROLE_PERMISSIONS.employee;
  const availableTabs = userPermissions.tabs;

  // Auto-connect socket when authenticated
  useEffect(() => {
    if (state.isAuthenticated && state.currentTenant && !state.socket) {
      // Socket connection is handled in the context
    }
  }, [state.isAuthenticated, state.currentTenant, state.socket]);

  // Ensure active tab is available to user
  useEffect(() => {
    if (!availableTabs.includes(activeTab)) {
      setActiveTab(availableTabs[0] || 'pos');
    }
  }, [activeTab, availableTabs]);

  // Handle tab changes with loading state
  const handleTabChange = async (newTab: string) => {
    if (newTab === activeTab) return;

    setIsLoading(true);
    setError(null);

    try {
      // Simulate loading time for better UX
      await new Promise(resolve => setTimeout(resolve, 200));
      setActiveTab(newTab);
    } catch (err) {
      setError('Failed to load tab content');
      console.error('Tab change error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'pos':
        return (
          <div className="flex h-full bg-gray-50">
            {/* Product Grid Section */}
            <div className="flex-1 p-4">
              <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
                  <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                    <span className="text-xl mr-2">🛍️</span>
                    Product Catalog
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">Select items to add to order</p>
                </div>
                <div className="flex-1 overflow-hidden">
                  <UnifiedProductGrid />
                </div>
              </div>
            </div>

            {/* Order Panel Section */}
            <div className="w-96 p-4 pl-0">
              <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50">
                  <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                    <span className="text-xl mr-2">🛒</span>
                    Current Order
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">Review and process order</p>
                </div>
                <div className="flex-1 overflow-hidden">
                  <EnhancedOrderPanelWithFloor />
                </div>
              </div>
            </div>
          </div>
        );
      case 'floor':
        return (
          <div className="h-full bg-gray-50 p-4">
            <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                      <span className="text-xl mr-2">🏢</span>
                      Restaurant Floor Layout
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">Manage tables, reservations, and seating arrangements</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      Phase 3 Enhanced
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <EnhancedFloorLayoutManager />
              </div>
            </div>
          </div>
        );
      case 'inventory':
        return userPermissions.features.includes('inventory_management') || userPermissions.features.includes('all')
          ? (userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('all')
              ? <Phase3InventoryManagement />
              : <UnifiedInventory />)
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'staff':
        return userPermissions.features.includes('staff_management') || userPermissions.features.includes('all')
          ? <UnifiedStaffScheduling />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'loyalty':
        return userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('all')
          ? <Phase3CRMSystem />
          : (
            <ComingSoonPlaceholder
              title="Customer Loyalty Program"
              description="Manage customer rewards and loyalty points"
              icon="loyalty"
              features={[
                'Customer registration and profiles',
                'Points-based reward system',
                'Tier-based benefits',
                'Promotional campaigns',
                'Loyalty analytics and insights'
              ]}
            />
          );
      case 'analytics':
        return userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('all')
          ? <Phase3AdvancedAnalytics />
          : (
            <ComingSoonPlaceholder
              title="Advanced Analytics"
              description="Detailed business insights and reporting"
              icon="analytics"
              features={[
                'Sales performance analytics',
                'Customer behavior insights',
                'Product performance reports',
                'Revenue forecasting',
                'Custom dashboard creation'
              ]}
            />
          );
      case 'reports':
        return userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('all')
          ? <Phase3ReportingSystem />
          : (
            <ComingSoonPlaceholder
              title="Sales Reports"
              description="Comprehensive sales and financial reporting"
              icon="analytics"
              features={[
                'Daily, weekly, monthly reports',
                'Revenue and profit analysis',
                'Tax reporting',
                'Export to PDF/Excel',
                'Automated report scheduling'
              ]}
            />
          );
      case 'settings':
        return (
          <ComingSoonPlaceholder
            title="System Settings"
            description="Configure system preferences and settings"
            icon="settings"
            features={[
              'Restaurant information setup',
              'Tax and pricing configuration',
              'Payment method settings',
              'User permissions management',
              'System backup and restore'
            ]}
          />
        );
      case 'kitchen':
        return <AdvancedKitchenDisplay />;
      case 'orders':
        return <OrderQueue />;
      case 'tabs':
        return <BarTabManagement />;
      case 'locations':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <MultiLocationDashboard />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'central-inventory':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <CentralizedInventory />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'advanced-analytics':
        return userPermissions.features.includes('all') || userPermissions.features.includes('advanced_analytics')
          ? <AdvancedAnalyticsDashboard />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'table-performance':
        return userPermissions.features.includes('all') || userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('table_analytics')
          ? <TablePerformanceAnalytics />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'server-performance':
        return userPermissions.features.includes('all') || userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('table_analytics')
          ? <ServerPerformanceTracker />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'operational-insights':
        return userPermissions.features.includes('all') || userPermissions.features.includes('advanced_analytics') || userPermissions.features.includes('table_analytics')
          ? <OperationalInsightsDashboard />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'multi-location':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <MultiLocationManager />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'cross-location-analytics':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <CrossLocationAnalytics />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'payment-analytics':
        return userPermissions.features.includes('all') || userPermissions.features.includes('payment_management') || userPermissions.features.includes('payment_processing')
          ? <PaymentAnalyticsDashboard />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'payment-history':
        return userPermissions.features.includes('all') || userPermissions.features.includes('payment_management') || userPermissions.features.includes('payment_processing')
          ? <ComingSoonPlaceholder
              title="Payment History"
              description="View detailed payment transaction history and manage refunds"
              icon="analytics"
              features={["Transaction Search", "Refund Management", "Payment Reports", "Export Data"]}
            />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'tenant-admin':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <EnhancedTenantAdminLandingPage
              onSwitchToPOS={() => setActiveTab('pos')}
              onLogout={() => window.location.reload()}
            />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'payment-terminals':
        return <PaymentTerminalManager />;
      case 'digital-wallets':
        return <DigitalWalletPayments />;
      case 'hardware':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? <POSHardwareManager />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'printers':
        return <ReceiptPrinterManager />;
      case 'scanners':
        return <BarcodeScannerManager />;
      case 'menu':
        return userPermissions.features.includes('all') || userPermissions.features.includes('tenant_management')
          ? (
            <ComingSoonPlaceholder
              title="Online Menu Management"
              description="Manage digital menu and online ordering"
              icon="menu"
              features={[
                'Digital menu creation',
                'Online ordering system',
                'Menu item management',
                'Pricing and availability control',
                'Customer-facing menu portal'
              ]}
            />
          )
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'qr':
        return userPermissions.features.includes('all')
          ? (
            <ComingSoonPlaceholder
              title="QR Code Generator"
              description="Generate QR codes for tables and menus"
              icon="qr"
              features={[
                'Table-specific QR codes',
                'Menu QR code generation',
                'Contactless ordering setup',
                'QR code customization',
                'Print-ready QR code formats'
              ]}
            />
          )
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'phase4-payments':
        return userPermissions.features.includes('all') || userPermissions.features.includes('payment_processing')
          ? <div className="p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Phase 4: Enhanced Payment Processing</h2>
                <p className="text-gray-600">Advanced payment processing with Stripe/Moneris integration, split payments, and real-time analytics</p>
              </div>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <span className="text-2xl">💳</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Enhanced Payment System</h3>
                      <p className="text-sm text-gray-600">99.5% success rate • &lt;3 second processing • Split payments</p>
                    </div>
                  </div>
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Phase 4 Ready
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">Payment Methods</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Stripe Integration</li>
                      <li>• Moneris (Canadian)</li>
                      <li>• Digital Wallets</li>
                      <li>• Cash Handling</li>
                    </ul>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">Advanced Features</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Split Bill Payments</li>
                      <li>• Tip Processing</li>
                      <li>• Fee Calculation</li>
                      <li>• Real-time Validation</li>
                    </ul>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• &lt;3 Second Processing</li>
                      <li>• 99.5% Success Rate</li>
                      <li>• Real-time Analytics</li>
                      <li>• Audit Trails</li>
                    </ul>
                  </div>
                </div>
                <div className="mt-4 text-center">
                  <p className="text-sm text-blue-600 font-medium">
                    🚀 Phase 4 payment processing is integrated and ready for use in the POS system
                  </p>
                </div>
              </div>
            </div>
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'phase4-hardware':
        return userPermissions.features.includes('all') || userPermissions.features.includes('hardware_management')
          ? <Phase4HardwareManager />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      case 'workflow-tester':
        return userPermissions.features.includes('all')
          ? <DineInWorkflowTester />
          : <div className="p-4 text-center text-gray-500">Access Denied: Insufficient Permissions</div>;
      default:
        return showDineInWorkflow ? (
          <UnifiedDineInWorkflowManager
            onWorkflowComplete={(orderData) => {
              console.log('Dine-in workflow completed:', orderData);
              setShowDineInWorkflow(false);
              // Optionally refresh orders or show success message
            }}
            onCancel={() => setShowDineInWorkflow(false)}
          />
        ) : (
          <div className="flex h-full">
            <div className="flex-1">
              <UnifiedProductGrid />
            </div>
            <div className="w-96 border-l border-gray-200">
              <UnifiedOrderPanel
                onDineInOrderStart={() => setShowDineInWorkflow(true)}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
              title="Toggle Sidebar"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {state.currentTenant?.name || 'Enterprise POS'}
                </h1>
                <p className="text-sm text-gray-500">Multi-Tenant Restaurant System</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full border border-blue-200">
                {state.currentEmployee?.role?.replace('_', ' ').toUpperCase()}
              </span>
              {state.socket && (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {state.currentEmployee?.name || 'User'}
              </div>
              <div className="text-xs text-gray-500">
                {state.currentTenant?.name || 'Demo Restaurant'}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setActiveTab('settings')}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
                title="Settings"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 shadow-sm"
              >
                <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6">
          <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
            {availableTabs.map((tab) => {
              const tabConfig = {
                pos: { label: 'Point of Sale', icon: '🏪', color: 'blue' },
                floor: { label: 'Floor Layout', icon: '🏢', color: 'green' },
                inventory: { label: 'Inventory', icon: '📦', color: 'orange' },
                staff: { label: 'Staff', icon: '👥', color: 'purple' },
                loyalty: { label: 'Loyalty', icon: '⭐', color: 'yellow' },
                analytics: { label: 'Analytics', icon: '📊', color: 'indigo' },
                reports: { label: 'Reports', icon: '📈', color: 'pink' },
                settings: { label: 'Settings', icon: '⚙️', color: 'gray' },
                kitchen: { label: 'Kitchen Display', icon: '🍳', color: 'red' },
                orders: { label: 'Order Queue', icon: '📋', color: 'blue' },
                tabs: { label: 'Bar Tabs', icon: '🍺', color: 'amber' },
                locations: { label: 'Multi-Location', icon: '🌍', color: 'emerald' },
                'central-inventory': { label: 'Central Inventory', icon: '🏭', color: 'orange' },
                'advanced-analytics': { label: 'Advanced Analytics', icon: '🔬', color: 'violet' },
                'table-performance': { label: 'Table Performance', icon: '📊', color: 'cyan' },
                'server-performance': { label: 'Server Performance', icon: '⚡', color: 'emerald' },
                'operational-insights': { label: 'Operational Insights', icon: '🎯', color: 'violet' },
                'multi-location': { label: 'Location Manager', icon: '🏢', color: 'rose' },
                'cross-location-analytics': { label: 'Cross-Location Analytics', icon: '🔄', color: 'amber' },
                'payment-analytics': { label: 'Payment Analytics', icon: '💳', color: 'indigo' },
                'payment-history': { label: 'Payment History', icon: '📜', color: 'gray' },
                'tenant-admin': { label: 'Restaurant Admin', icon: '👑', color: 'purple' },
                'payment-terminals': { label: 'Payment Terminals', icon: '💰', color: 'green' },
                'digital-wallets': { label: 'Digital Wallets', icon: '📱', color: 'blue' },
                hardware: { label: 'POS Hardware', icon: '🖥️', color: 'gray' },
                printers: { label: 'Receipt Printers', icon: '🖨️', color: 'slate' },
                scanners: { label: 'Barcode Scanners', icon: '📷', color: 'zinc' },
                menu: { label: 'Online Menu', icon: '📋', color: 'orange' },
                qr: { label: 'QR Codes', icon: '📱', color: 'black' },
                'phase4-payments': { label: 'Phase 4 Payments', icon: '💳', color: 'blue' },
                'phase4-hardware': { label: 'Phase 4 Hardware', icon: '🔧', color: 'green' },
                'workflow-tester': { label: 'Workflow Tester', icon: '🧪', color: 'purple' }
              };

              const config = tabConfig[tab as keyof typeof tabConfig] || { label: tab, icon: '📄', color: 'gray' };
              const isActive = activeTab === tab;

              return (
                <button
                  key={tab}
                  onClick={() => handleTabChange(tab)}
                  disabled={isLoading}
                  className={`flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                    isActive
                      ? `border-${config.color}-500 text-${config.color}-600 bg-${config.color}-50`
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  title={config.label}
                >
                  <span className="text-base">{config.icon}</span>
                  <span className="hidden sm:inline">{config.label}</span>
                  {isLoading && activeTab === tab && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin ml-1"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="flex-1 overflow-hidden bg-gray-50">
        <div className="h-full p-4">
          <div className="h-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={() => {
                      setError(null);
                      setActiveTab('pos');
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    Go to POS
                  </button>
                </div>
              </div>
            ) : isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading...</h3>
                  <p className="text-gray-600">Please wait while we load the content</p>
                </div>
              </div>
            ) : (
              renderTabContent()
            )}
          </div>
        </div>
      </main>

      {/* Enhanced Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-6 py-3 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Orders: <span className="font-bold text-blue-600">{state.orders.length}</span>
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Products: <span className="font-bold text-green-600">{state.products.length}</span>
              </span>
            </div>

            {state.socket ? (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-emerald-700">Real-time Connected</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                <span className="text-sm font-medium text-red-700">Offline Mode</span>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Active Tab: <span className="font-bold text-purple-600 capitalize">{activeTab.replace('-', ' ')}</span>
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
            <div className="text-sm font-mono text-gray-700">
              {new Date().toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}
            </div>
            <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
              v2.0.0
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Main App Content Component
const UnifiedPOSContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  const { state } = useEnhancedAppContext();
  const [showRegistration, setShowRegistration] = useState(false);

  // Update login state based on authentication
  useEffect(() => {
    setIsLoggedIn(state.isAuthenticated);
  }, [state.isAuthenticated, setIsLoggedIn]);

  if (!isLoggedIn) {
    console.log('🔐 UnifiedPOSContent: User not logged in, showing login screen');
    if (showRegistration) {
      console.log('📝 UnifiedPOSContent: Showing registration screen');
      return (
        <UserRegistration
          onBack={() => setShowRegistration(false)}
          onSuccess={(data) => {
            console.log('Registration successful:', data);
            // Auto-login after successful registration
            setIsLoggedIn(true);
            setShowRegistration(false);
          }}
        />
      );
    }

    console.log('🎨 UnifiedPOSContent: Rendering EnhancedLogin component');
    return (
      <EnhancedLogin
        onLogin={setIsLoggedIn}
        onShowRegistration={() => setShowRegistration(true)}
      />
    );
  }

  return <UnifiedPOSInterface />;
};

// Main Unified POS System Component
const UnifiedPOSSystem: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <EnhancedAppProvider>
      <UnifiedPOSContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
    </EnhancedAppProvider>
  );
};

export default UnifiedPOSSystem;
