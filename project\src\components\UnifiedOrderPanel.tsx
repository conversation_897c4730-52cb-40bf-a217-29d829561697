import React, { useState } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { X, Plus, Minus, CreditCard, DollarSign, ShoppingBag, Tag, Zap, MapPin } from 'lucide-react';
import Phase4EnhancedPaymentProcessor from './Phase4EnhancedPaymentProcessor';
import { Table } from '../types';

interface UnifiedOrderPanelProps {
  onDineInOrderStart?: () => void;
  selectedTable?: Table | null;
  onOrderComplete?: (orderData: any) => void;
}

const UnifiedOrderPanel: React.FC<UnifiedOrderPanelProps> = ({
  onDineInOrderStart,
  selectedTable,
  onOrderComplete
}) => {
  const { state, dispatch, apiCall } = useEnhancedAppContext();
  const [showPayment, setShowPayment] = useState(false);
  const [showPhase4Payment, setShowPhase4Payment] = useState(false);
  const [tip, setTip] = useState(0);
  const [tabName, setTabName] = useState('');
  const [isEditingTab, setIsEditingTab] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderType, setOrderType] = useState<'dine_in' | 'takeout' | 'delivery'>('dine_in');
  
  const currentOrder = state.currentOrder;
  
  const handleRemoveItem = (itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM_FROM_ORDER', payload: itemId });
  };
  
  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(itemId);
    } else {
      dispatch({
        type: 'UPDATE_ITEM_QUANTITY',
        payload: { id: itemId, quantity: newQuantity }
      });
    }
  };
  
  const handleClearOrder = () => {
    dispatch({ type: 'CLEAR_CURRENT_ORDER' });
    setShowPayment(false);
    setShowPhase4Payment(false);
    setTip(0);
  };

  const handlePhase4PaymentComplete = (result: any) => {
    console.log('✅ Phase 4 Payment completed:', result);

    // Clear the current order
    dispatch({ type: 'CLEAR_CURRENT_ORDER' });

    // Add to order history
    const completedOrder = {
      id: result.transaction_id,
      items: currentOrder?.items || [],
      subtotal: currentOrder?.subtotal || 0,
      tax: currentOrder?.tax || 0,
      total: currentOrder?.total || 0,
      payment_method: 'Phase 4 Enhanced',
      order_type: orderType,
      table_id: selectedTable?.id,
      table_number: selectedTable?.number,
      completed_at: new Date().toISOString(),
      processing_time: result.processing_time
    };

    dispatch({ type: 'ADD_ORDER_TO_HISTORY', payload: completedOrder });

    // Reset states
    setShowPhase4Payment(false);
    setShowPayment(false);
    setTip(0);
    setTabName('');
    setIsEditingTab(false);

    // Call completion callback if provided
    if (onOrderComplete) {
      onOrderComplete(completedOrder);
    }

    // Show success message
    alert(`✅ Phase 4 Payment successful! Transaction: ${result.transaction_id}`);
  };

  const handlePhase4PaymentCancel = () => {
    setShowPhase4Payment(false);
  };
  
  const handlePayment = async (method: 'cash' | 'card' | 'mobile') => {
    if (!currentOrder) return;
    
    setIsProcessing(true);
    
    try {
      const orderData = {
        items: currentOrder.items,
        subtotal: currentOrder.subtotal,
        tax: currentOrder.tax,
        tip: tip,
        total: currentOrder.subtotal + currentOrder.tax + tip,
        payment_method: method,
        order_type: orderType,
        table_id: selectedTable?.id,
        table_number: selectedTable?.number,
        guest_count: selectedTable?.guestCount,
        tab_name: currentOrder.tabName || tabName || undefined
      };
      
      console.log('💳 Processing payment:', orderData);
      
      const response = await apiCall('/api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });
      
      if (response.ok) {
        const completedOrder = await response.json();
        console.log('✅ Order completed:', completedOrder);
        
        // Clear the current order
        dispatch({ type: 'CLEAR_CURRENT_ORDER' });
        
        // Add to order history
        dispatch({ type: 'ADD_ORDER_TO_HISTORY', payload: completedOrder });

        // Reset states
        setShowPayment(false);
        setTip(0);
        setTabName('');
        setIsEditingTab(false);

        // Call completion callback if provided
        if (onOrderComplete) {
          onOrderComplete(completedOrder);
        }

        // Show success message (you could add a toast notification here)
        alert(`Payment successful! Order #${completedOrder.id} completed.`);
      } else {
        console.error('❌ Payment failed');
        alert('Payment failed. Please try again.');
      }
    } catch (error) {
      console.error('💥 Payment error:', error);
      alert('Payment error. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleSaveTab = () => {
    if (tabName.trim()) {
      dispatch({ type: 'SET_TAB_NAME', payload: tabName.trim() });
    }
    setIsEditingTab(false);
  };
  
  const calculateTipAmount = (percentage: number) => {
    if (!currentOrder) return 0;
    return Math.round(currentOrder.subtotal * percentage * 100) / 100;
  };
  
  // If there's no current order, show empty state
  if (!currentOrder || currentOrder.items.length === 0) {
    return (
      <div className="bg-white border-l border-gray-200 p-6 flex flex-col items-center justify-center h-full text-center">
        <ShoppingBag className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Items in Order</h3>
        <p className="text-gray-500">
          Add items from the menu to create a new order
        </p>
      </div>
    );
  }

  // Phase 4 Enhanced Payment Processor
  if (showPhase4Payment) {
    const orderData = {
      id: `order_${Date.now()}`,
      items: currentOrder.items,
      subtotal: currentOrder.subtotal,
      tax: currentOrder.tax,
      total: currentOrder.total,
      tableId: undefined,
      tableNumber: undefined,
      guestCount: currentOrder.items.reduce((sum, item) => sum + item.quantity, 0),
      serverName: state.currentEmployee?.name || 'Unknown'
    };

    return (
      <Phase4EnhancedPaymentProcessor
        orderData={orderData}
        onPaymentComplete={handlePhase4PaymentComplete}
        onCancel={handlePhase4PaymentCancel}
      />
    );
  }
  
  // Payment screen
  if (showPayment) {
    return (
      <div className="bg-white border-l border-gray-200 p-6 flex flex-col h-full">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Payment</h3>
        
        {/* Order Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Subtotal</span>
            <span className="text-gray-900">${currentOrder.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Tax</span>
            <span className="text-gray-900">${currentOrder.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between font-semibold">
            <span className="text-gray-600">Tip</span>
            <span className="text-blue-600">${tip.toFixed(2)}</span>
          </div>
          <div className="border-t border-gray-300 my-2"></div>
          <div className="flex justify-between font-bold">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">${(currentOrder.subtotal + currentOrder.tax + tip).toFixed(2)}</span>
          </div>
        </div>
        
        {/* Tip options */}
        <div className="mb-6">
          <h4 className="text-gray-900 mb-3 font-medium">Add Tip</h4>
          <div className="grid grid-cols-4 gap-2">
            {[0, 0.15, 0.18, 0.20].map((percentage, index) => (
              <button
                key={index}
                className={`py-2 rounded-md font-semibold transition-colors ${
                  tip === calculateTipAmount(percentage)
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                }`}
                onClick={() => setTip(calculateTipAmount(percentage))}
              >
                {percentage === 0 ? 'None' : `${percentage * 100}%`}
              </button>
            ))}
          </div>
          <div className="mt-3 flex items-center">
            <span className="text-gray-600 mr-2">Custom:</span>
            <input
              type="number"
              min="0"
              step="0.01"
              value={tip}
              onChange={(e) => setTip(Number(e.target.value))}
              className="bg-white border border-gray-300 text-gray-900 px-3 py-2 rounded-md w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        
        {/* Payment methods */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('cash')}
            disabled={isProcessing}
          >
            <DollarSign className="h-8 w-8 text-green-500 mb-2" />
            <span className="text-gray-900">Cash</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('card')}
            disabled={isProcessing}
          >
            <CreditCard className="h-8 w-8 text-blue-500 mb-2" />
            <span className="text-gray-900">Card</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('mobile')}
            disabled={isProcessing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-500 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
              <path d="M12 18h.01" />
            </svg>
            <span className="text-gray-900">Mobile</span>
          </button>
        </div>
        
        {isProcessing && (
          <div className="mb-4 text-center">
            <div className="inline-flex items-center">
              <svg className="animate-spin h-4 w-4 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span className="text-gray-600">Processing payment...</span>
            </div>
          </div>
        )}
        
        {/* Back button */}
        <button
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 rounded-lg mt-auto transition-colors border border-gray-300 disabled:opacity-50"
          onClick={() => setShowPayment(false)}
          disabled={isProcessing}
        >
          Back to Order
        </button>
      </div>
    );
  }
  
  // Order view
  return (
    <div className="bg-white border-l border-gray-200 p-4 flex flex-col h-full">
      {/* Order header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h3 className="text-xl font-semibold text-gray-900">Current Order</h3>
          {currentOrder.tabName && !isEditingTab && (
            <div className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center">
              <Tag className="h-3 w-3 mr-1" />
              {currentOrder.tabName}
            </div>
          )}
        </div>
        <button
          className="text-red-500 hover:text-red-600 transition-colors"
          onClick={handleClearOrder}
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      
      {/* Order Type Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Order Type</label>
        <div className="grid grid-cols-3 gap-2">
          {[
            { id: 'dine_in', label: 'Dine In', icon: '🍽️' },
            { id: 'takeout', label: 'Takeout', icon: '🥡' },
            { id: 'delivery', label: 'Delivery', icon: '🚚' }
          ].map((type) => (
            <button
              key={type.id}
              onClick={() => {
                if (type.id === 'dine_in' && onDineInOrderStart && !selectedTable) {
                  onDineInOrderStart();
                } else {
                  setOrderType(type.id as any);
                }
              }}
              className={`p-2 rounded-md text-sm font-medium transition-colors border ${
                orderType === type.id
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="text-center">
                <div className="text-lg mb-1">{type.icon}</div>
                <div>{type.label}</div>
                {type.id === 'dine_in' && !selectedTable && (
                  <div className="text-xs text-gray-500 mt-1">Select Table</div>
                )}
              </div>
            </button>
          ))}
        </div>

        {/* Selected Table Display */}
        {selectedTable && orderType === 'dine_in' && (
          <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-blue-600" />
              <div className="text-sm">
                <span className="font-medium text-blue-900">Table {selectedTable.number}</span>
                <span className="text-blue-700 ml-2">
                  ({selectedTable.seats} seats • {selectedTable.section || 'General'})
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tab name editor */}
      {isEditingTab ? (
        <div className="flex mb-4">
          <input
            type="text"
            value={tabName}
            onChange={(e) => setTabName(e.target.value)}
            placeholder="Enter tab name"
            className="flex-grow bg-white border border-gray-300 text-gray-900 px-3 py-2 rounded-l-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            autoFocus
          />
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-r-md transition-colors"
            onClick={handleSaveTab}
          >
            Save
          </button>
        </div>
      ) : (
        <button
          className="mb-4 bg-gray-50 hover:bg-gray-100 text-gray-600 px-3 py-2 rounded-md text-sm flex items-center transition-colors border border-gray-200"
          onClick={() => {
            setTabName(currentOrder.tabName || '');
            setIsEditingTab(true);
          }}
        >
          <Tag className="h-4 w-4 mr-2" />
          {currentOrder.tabName ? 'Edit Tab Name' : 'Add Tab Name'}
        </button>
      )}
      
      {/* Items list */}
      <div className="flex-grow overflow-y-auto mb-4">
        {currentOrder.items.map(item => (
          <div key={item.id} className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-2 flex items-center">
            <div className="flex-grow">
              <div className="flex justify-between">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                <button
                  className="text-gray-400 hover:text-red-500 transition-colors"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center bg-white border border-gray-300 rounded-md">
                  <button
                    className="px-2 py-1 text-gray-500 hover:text-gray-700 transition-colors"
                    onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-2 text-gray-900">{item.quantity}</span>
                  <button
                    className="px-2 py-1 text-gray-500 hover:text-gray-700 transition-colors"
                    onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="text-blue-600 font-semibold">
                  ${(item.price * item.quantity).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Order totals */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
        <div className="flex justify-between mb-1">
          <span className="text-gray-600">Subtotal</span>
          <span className="text-gray-900">${currentOrder.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-600">Tax (8%)</span>
          <span className="text-gray-900">${currentOrder.tax.toFixed(2)}</span>
        </div>
        <div className="border-t border-gray-300 my-2"></div>
        <div className="flex justify-between font-bold">
          <span className="text-gray-900">Total</span>
          <span className="text-gray-900">${currentOrder.total.toFixed(2)}</span>
        </div>
      </div>
      
      {/* Checkout buttons */}
      <div className="space-y-3">
        <button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center transition-colors"
          onClick={() => setShowPhase4Payment(true)}
        >
          <Zap className="h-5 w-5 mr-2" />
          Phase 4 Enhanced Payment
        </button>

        <button
          className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center transition-colors"
          onClick={() => setShowPayment(true)}
        >
          <CreditCard className="h-5 w-5 mr-2" />
          Standard Payment
        </button>
      </div>
    </div>
  );
};

export default UnifiedOrderPanel;
